import 'package:flutter/material.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_widgets.dart';

class LocationService extends StatefulWidget {
  const LocationService({Key? key}) : super(key: key);

  @override
  State<LocationService> createState() => _LocationServiceState();
}

class _LocationServiceState extends State<LocationService> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: body(),
    );
  }
  //region Body
  Widget body(){
    return  Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          verticalSizedBox(10),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 5),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.all(Radius.circular(80)),
              color: AppColors.lightWhite3,
            ),
            child: Center(
              child: appText(AppStrings.detectMyLocation,color: AppColors.writingColor3,

                fontWeight:FontWeight.w700,
                fontSize: 13,
                fontFamily: AppConstants.rRegular,


              ),
            ),
          )
        ],
      ),
    );
  }
//endregion

}
