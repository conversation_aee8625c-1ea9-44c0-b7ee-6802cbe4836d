import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/seller/edit_product/edit_product_bloc.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_search_field/app_search_field.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

// region Seller Edit Product Screen
class EditProductScreen extends StatefulWidget {
  final int storeId;
  final String storeRef;

  const EditProductScreen(
      {Key? key, required this.storeId, required this.storeRef})
      : super(key: key);

  @override
  _EditProductScreenState createState() => _EditProductScreenState();
}
// endregion

class _EditProductScreenState extends State<EditProductScreen> {
  //Widget
  double width = 0.0;
  // region Bloc
  late EditProductBloc editProductBloc;

  // endregion

  // region Init
  @override
  void initState() {
    editProductBloc = EditProductBloc(context, widget.storeId, widget.storeRef);
    editProductBloc.init();
    super.initState();
  }

  // endregion

  //region Dispose
  @override
  void dispose() {
    editProductBloc.dispose();
    super.dispose();
  }

  //endregion

  // region build
  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (BuildContext context, BoxConstraints constraints) {
        width = constraints.maxWidth;
        return GestureDetector(
          onTap: () {
            CommonMethods.closeKeyboard(context);
          },
          child: Scaffold(
            backgroundColor: AppColors.appWhite,
            floatingActionButton: options(),
            floatingActionButtonLocation:
                FloatingActionButtonLocation.centerFloat,
            appBar: appBar(),
            resizeToAvoidBottomInset: true,
            body: SafeArea(child: body()),
          ),
        );
      },
    );
  }

  // endregion

  //region Appbar
  AppBar appBar() {
    return AppCommonWidgets.mainAppBar(
      context: context,
      isCustomTitle: false,
      title: AppStrings.editProduct,
      isDefaultMenuVisible: true,
      isMembershipVisible: false,
      isCartVisible: false,
      isCustomMenuVisible: false,
    );
  }

  //endregion

  //region Options
  Widget options() {
    return StreamBuilder<bool>(
        stream: editProductBloc.selectCtrl.stream,
        builder: (context, snapshot) {
          return Visibility(
            visible: editProductBloc.isMultiSelect,
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 20),
              decoration: BoxDecoration(
                  color: AppColors.appWhite,
                  borderRadius: BorderRadius.circular(10)),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  InkWell(
                    onTap: () {
                      editProductBloc.productReferenceList.isEmpty
                          ? CommonMethods.toastMessage(
                              AppStrings.pleaseSelectAProduct, context)
                          : editProductBloc.updateStockDialog();
                      //print("update stock");
                    },
                    child: Container(
                      padding: const EdgeInsets.all(10),
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.only(
                            topLeft: Radius.circular(10),
                            bottomLeft: Radius.circular(10),
                          ),
                          color: AppColors.brandBlack),
                      child: Text(
                        AppStrings.updateStock,
                        style: AppTextStyle.contentText0(
                            textColor: AppColors.appWhite),
                      ),
                    ),
                  ),
                  InkWell(
                    onTap: () async {
                      await Future.delayed(Duration.zero);
                      editProductBloc.productReferenceList.isEmpty
                          ? CommonMethods.toastMessage(
                              AppStrings.pleaseSelectAProduct, context)
                          : editProductBloc.goToEditProductDetails();
                    },
                    child: Container(
                      padding: const EdgeInsets.all(10),
                      margin: const EdgeInsets.symmetric(horizontal: 1),
                      decoration:
                          BoxDecoration(color: AppColors.brandBlack),
                      child: Text(
                        "Edit details",
                        style: AppTextStyle.contentText0(
                            textColor: AppColors.appWhite),
                      ),
                    ),
                  ),
                  InkWell(
                    onTap: () {
                      editProductBloc.productReferenceList.isEmpty
                          ? CommonMethods.toastMessage(
                              AppStrings.pleaseSelectAProduct, context)
                          : editProductBloc.onTapDelete();
                    },
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                          vertical: 10, horizontal: 20),
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.only(
                            topRight: Radius.circular(10),
                            bottomRight: Radius.circular(10),
                          ),
                          color: AppColors.brandBlack),
                      child: Text(
                        "Delete",
                        style: AppTextStyle.contentText0(
                            textColor: AppColors.appWhite),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        });
  }

  //endregion

  // region Body
  Widget body() {
    return StreamBuilder<EditProductState>(
        stream: editProductBloc.editProductCtrl.stream,
        initialData: EditProductState.Loading,
        builder: (context, snapshot) {
          if (snapshot.data == EditProductState.Loading) {
            return Center(child: AppCommonWidgets.appCircularProgress());
          }
          if (snapshot.data == EditProductState.Empty) {
            return Center(child: noProducts());
          }
          if (snapshot.data == EditProductState.Success) {
            return Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                searchBar(),
                selectEdit(),
                Expanded(child: productList()),
              ],
            );
            // return Column(
            //   mainAxisSize: MainAxisSize.min,
            //   children: [
            //     searchBar(),
            //     selectEdit(),
            //     Expanded(child: productList()),
            //   ],
            // );
          }
          return Center(
              child: AppCommonWidgets.errorMessage(
                  error: AppStrings.commonErrorMessage));
        });
  }

  // endregion

  //region Searchbar
  Widget searchBar() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 10),
      child: AppSearchField(
        textEditingController: editProductBloc.searchTextCtrl,
        hintText: AppStrings.searchProducts,
        onChangeText: (v) {
          editProductBloc
              .onChangeSearchField(editProductBloc.searchTextCtrl.text);
        },
        onTapSuffix: () {
          editProductBloc
              .onChangeSearchField(editProductBloc.searchTextCtrl.text);
        },
      ),
    );
  }

//endregion

  //region View and Edit - Click
  Widget viewClickEdit() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 15),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
        decoration: BoxDecoration(
          color: AppColors.appWhite,
          borderRadius: const BorderRadius.all(Radius.circular(30)),
          boxShadow: [
            BoxShadow(
              offset: const Offset(0, 1),
              blurRadius: 5,
              color: AppColors.appBlack.withOpacity(0.2),
            ),
          ],
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SvgPicture.asset(
              AppImages.yellowLightBulb,
              fit: BoxFit.cover,
            ),
            horizontalSizedBox(5),
            Expanded(
              child: FittedBox(
                child: Text(
                  "View & edit - click | Reorder - hold & drag | Select - hold",
                  style: TextStyle(
                      fontSize: 12,
                      fontFamily: "LatoRegular",
                      fontWeight: FontWeight.w400,
                      color: AppColors.appBlack),
                ),
              ),
            ),
            horizontalSizedBox(5),
            Icon(Icons.close, color: AppColors.darkGray, size: 20)
          ],
        ),
      ),
    );
  }

  //endregion

  //region Select and Edit
  Widget selectEdit() {
    return Row(
      mainAxisSize: MainAxisSize.max,
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Expanded(
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
            margin: const EdgeInsets.symmetric(vertical: 10),
            child: Text(
              AppStrings.selectAndEdit,
              style: AppTextStyle.contentHeading0(
                  textColor: AppColors.writingBlack0),
            ),
          ),
        ),
        // CupertinoButton(
        //     padding: EdgeInsets.zero,
        //     onPressed: () {},
        //     child: Container(
        //         decoration: BoxDecoration(
        //           color: AppColors.white,
        //           borderRadius: BorderRadius.all(Radius.circular(5)),
        //           boxShadow: [
        //             BoxShadow(
        //               offset: const Offset(0, 1),
        //               blurRadius: 5,
        //               color: AppColors.appBlack.withOpacity(0.2),
        //             ),
        //           ],
        //         ),
        //         child: SvgPicture.asset(
        //           AppImages.list,
        //           fit: BoxFit.cover,
        //         ))),
        // horizontalSizedBox(10),
        // CupertinoButton(
        //     padding: EdgeInsets.zero,
        //     onPressed: () {},
        //     child: Container(
        //         decoration: BoxDecoration(
        //           color: AppColors.lightGray,
        //           borderRadius: BorderRadius.all(Radius.circular(5)),
        //           boxShadow: [
        //             BoxShadow(
        //               offset: const Offset(0, 1),
        //               blurRadius: 5,
        //               color: AppColors.appBlack.withOpacity(0.2),
        //             ),
        //           ],
        //         ),
        //         child: SvgPicture.asset(
        //           AppImages.short,
        //           fit: BoxFit.cover,
        //         ))),
      ],
    );
  }

  //endregion

  //region Product List
  Widget productList() {
    return RefreshIndicator(
      color: AppColors.brandBlack,
      onRefresh: () async {
        await editProductBloc.getProductApiCall();
      },
      child: GridView.builder(
          addAutomaticKeepAlives: false,
          padding: const EdgeInsets.only(bottom: 20),
          addRepaintBoundaries: false,
          shrinkWrap: true,
          itemCount: editProductBloc.searchProductList.length,
          // itemCount: 6,
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            // childAspectRatio: MediaQuery.of(context).size.width / (MediaQuery.of(context).size.height / 1.6),
            crossAxisCount: 2,
            mainAxisSpacing: 0,
            crossAxisSpacing: 0,
            mainAxisExtent: (MediaQuery.of(context).size.width / 2) +
                CommonMethods.textHeight(
                    context: context,
                    textStyle: AppTextStyle.contentHeading0(
                        textColor: AppColors.appBlack)) +
                CommonMethods.textHeight(
                    context: context,
                    textStyle: AppTextStyle.contentHeading0(
                        textColor: AppColors.appBlack)) +
                CommonMethods.textHeight(
                    context: context,
                    textStyle:
                        AppTextStyle.access0(textColor: AppColors.appBlack)) +
                5,
          ),
          itemBuilder: (context, index) {
            return Container(
              padding: EdgeInsets.zero,
              decoration: BoxDecoration(
                borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(10),
                    topRight: Radius.circular(10)),
                color: AppColors.appWhite,
                border: Border.all(color: AppColors.lightestGrey2),
                boxShadow: [
                  BoxShadow(
                    offset: const Offset(0, 1),
                    blurRadius: 4,
                    color: AppColors.appBlack.withOpacity(0.1),
                  ),
                ],
              ),
              child: InkWell(
                onTap: () {
                  //If multiselsct is false then go to product screen. else select methord call
                  if (editProductBloc.isMultiSelect) {
                    editProductBloc.onTapProduct(editProductBloc
                        .searchProductList[index].productReference!);
                  } else {
                    editProductBloc.goToViewProductScreen(index);
                  }
                  //print(editProductBloc.storeProduct[index].productid);
                },
                onLongPress: () {
                  //If multiselect is false
                  if (!editProductBloc.isMultiSelect) {
                    editProductBloc.onLongPressProduct(
                        productReference: editProductBloc
                            .searchProductList[index].productReference!);
                  }
                },
                child: StreamBuilder<bool>(
                    stream: editProductBloc.selectCtrl.stream,
                    builder: (context, snapshot) {
                      return Stack(
                        alignment: Alignment.topLeft,
                        children: [
                          AppCommonWidgets.productCardInGrid(
                            // productImage: editProductBloc
                            //         .searchProductList[index]
                            //         .prodImages!
                            //         .isEmpty
                            //     ? ""
                            //     : editProductBloc.searchProductList[index]
                            //         .prodImages!.first.productImage,
                            // productBrand: editProductBloc
                            //     .searchProductList[index].brandName!,
                            // productName: editProductBloc
                            //     .searchProductList[index].productName!,
                            // sellingPrice: editProductBloc
                            //     .searchProductList[index].sellingPrice
                            //     .toString(),
                            // mrp: editProductBloc
                            //     .searchProductList[index].mrpPrice
                            //     .toString(),
                            context: context,
                            screenWidth: width,
                            product: editProductBloc.searchProductList[index],
                          ),
                          Visibility(
                            visible: editProductBloc.isMultiSelect,
                            child: Padding(
                              padding: const EdgeInsets.all(10),
                              child: Container(
                                height: 15,
                                width: 15,
                                decoration: BoxDecoration(
                                    color: editProductBloc.productReferenceList
                                            .contains(editProductBloc
                                                .searchProductList[index]
                                                .productReference!)
                                        ? AppColors.brandBlack
                                        : AppColors.appWhite,
                                    borderRadius: const BorderRadius.all(
                                        Radius.circular(10)),
                                    border: Border.all(
                                        color: AppColors.lightStroke)),
                              ),
                            ),
                          ),
                        ],
                      );
                    }),
              ),
            );
          }),
    );
  }

  //endregion

  //region Image
  Widget productImage(int index) {
    return ClipRRect(
        borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(10), topRight: Radius.circular(10)),
        child: Center(
            child: editProductBloc.searchProductList[index].prodImages!.isEmpty
                ? SvgPicture.asset(
                    AppImages.productPlaceHolder,
                    fit: BoxFit.fill,
                  )
                :
                // Image.network("${sellerViewStoreBloc.storeProductResponse.data![index].prodImages?[0].productImage.toString()}",
                //   fit: BoxFit.cover,
                //   width: double.infinity,
                //   height: double.infinity,
                //   filterQuality: FilterQuality.low,)

                extendedImage(
                    editProductBloc
                        .searchProductList[index].prodImages![0].productImage!,
                    customPlaceHolder: AppImages.productPlaceHolder,
                    context,
                    300,
                    300,
                    cache: true)

            // CachedNetworkImage(
            //   imageUrl: "${editProductBloc.storeProduct[index].prodImages?[0].productImage.toString()}",
            //   fit: BoxFit.cover,
            //   width: double.infinity,
            //   height: double.infinity,
            //   filterQuality: FilterQuality.low,
            //   placeholder: (context, url) =>  Center(child: Image.asset(AppImages.noImage)),
            //   errorWidget: (context, url, error) => const Icon(Icons.error),
            // ),

            // Image.network("${sellerViewStoreBloc.storeProductResponse.data![index].prodImages?[0].productImage.toString()}",fit: BoxFit.cover,
            // width: double.infinity,
            //   height: double.infinity,
            //   filterQuality: FilterQuality.low,
            //
            // )

            ));
  }

  //endregion

  //region Product Name
  Widget productName(int index) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 5),
      child: Text(
        "${editProductBloc.searchProductList[index].brandName} ${editProductBloc.searchProductList[index].productName}",
        maxLines: 2,
        style: TextStyle(
          fontFamily: "LatoRegular",
          fontSize: 14,
          fontWeight: FontWeight.w400,
          color: AppColors.appBlack.withOpacity(0.7),
        ),
      ),
    );
  }

  //endregion

  //region Product price
  Widget productPrice(int index) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 5),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            "₹ ${editProductBloc.searchProductList[index].sellingPrice.toString()}",
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w400,
              color: AppColors.appBlack,
              fontFamily: "LatoRegular",
            ),
          ),
          horizontalSizedBox(10),
          Text(
            "₹ ${editProductBloc.searchProductList[index].mrpPrice.toString()}",
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w400,
              color: AppColors.appBlack6,
              fontFamily: "LatoRegular",
              decoration: TextDecoration.lineThrough,
            ),
          ),
        ],
      ),
    );
  }
//endregion

  ///No products
  //region No products
  Widget noProducts() {
    return Container(
      alignment: Alignment.center,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SvgPicture.asset(
            AppImages.noProducts,
            height: 43,
            width: 43,
          ),
          Text(
            AppStrings.noProductYet,
            style: AppTextStyle.heading2Bold(textColor: AppColors.appBlack),
          ),
          Row(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                AppStrings.addProductAndStartSelling,
                style: AppTextStyle.heading4Regular(
                    textColor: AppColors.writingColor3),
              ),
              horizontalSizedBox(3),
              SvgPicture.asset(AppImages.smileEmoji)
            ],
          ),
          verticalSizedBox(20),
          CupertinoButton(
            onPressed: () {
              editProductBloc.goToAddProduct();
              //buyerViewStoreBloc.onTapGoToCheckList();
            },
            padding: EdgeInsets.zero,
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(9),
                color: AppColors.brandBlack,
              ),
              padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
              child: Text(
                AppStrings.addProduct,
                style: AppTextStyle.button1Bold(textColor: AppColors.appWhite),
              ),
            ),
          )
        ],
      ),
    );
  }
//endregion
}

// Container(
// height: 15,
// width: 15,
// decoration: BoxDecoration(
// color:snapshot.data== index ?AppColors.brandBlue:AppColors.white,
// borderRadius: BorderRadius.all(Radius.circular(10)),
// border: Border.all(color: AppColors.lightWhite2)
// ),
//
// ),
