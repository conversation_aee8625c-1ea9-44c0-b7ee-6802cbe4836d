import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/seller/seller_all_orders/components/cancel_dialog/cancel_dialog_bloc.dart';
import 'package:swadesic/features/seller/seller_all_orders/seller_sub_order/seller_sub_order_bloc.dart';
import 'package:swadesic/model/order_response/get_order_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_test_fields/app_text_fields.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_widgets.dart';

class CancelDialogScreen extends StatefulWidget {
  final SellerSubOrderBloc sellerSubOrderBloc;
  final List<String> subOrderNumbers;
  final Order order;
  final BuildContext? previousScreenContext;
  const CancelDialogScreen(
      {Key? key,
      required this.subOrderNumbers,
      required this.sellerSubOrderBloc,
      required this.order,
      required this.previousScreenContext})
      : super(key: key);

  @override
  State<CancelDialogScreen> createState() => _CancelDialogScreenState();
}

class _CancelDialogScreenState extends State<CancelDialogScreen> {
  //region Bloc
  late CancelDialogBloc cancelDialogBloc;
  //endregion

  //region Init
  @override
  void initState() {
    cancelDialogBloc = CancelDialogBloc(context, widget.sellerSubOrderBloc,
        widget.subOrderNumbers, widget.order, widget.previousScreenContext);
    cancelDialogBloc.init();
    // TODO: implement initState
    super.initState();
  }
  //endregion

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Stack(
          alignment: Alignment.center,
          children: [
            Text(
              "Cancel reason",
              style:
                  AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
            ),
            Align(
                alignment: Alignment.centerRight,
                child: SvgPicture.asset(
                  AppImages.exclamation,
                  fit: BoxFit.cover,
                ))
          ],
        ),
        verticalSizedBox(10),
        AppTextFields.allTextField(
          context: context,
          maxLines: 5,
          maxEntry: 200,
          minLines: 5,
          textEditingController: cancelDialogBloc.cancelReasonTextCtrl,
          hintText: AppStrings.cancelReason,
        ),
        verticalSizedBox(20),
        Row(
          children: [
            Expanded(
              child: StreamBuilder<bool>(
                stream: cancelDialogBloc.doneButtonLoadingCtrl.stream,
                initialData: false,
                builder: (context, snapshot) {
                  bool isLoading = snapshot.data ?? false;

                  return CupertinoButton(
                    onPressed: isLoading
                        ? null
                        : () async {
                            cancelDialogBloc.confirmAndCancelOrder(
                                selectedSubOrders: widget.subOrderNumbers);
                          },
                    padding: EdgeInsets.zero,
                    child: Container(
                      width: double.infinity,
                      padding: const EdgeInsets.symmetric(
                          vertical: 13, horizontal: 10),
                      decoration: BoxDecoration(
                        color: isLoading
                            ? AppColors.brandBlack.withOpacity(0.7)
                            : AppColors.brandBlack,
                        borderRadius: BorderRadius.circular(100),
                      ),
                      child: SizedBox(
                        height: 20, // Fixed height to prevent size changes
                        child: Center(
                          child: isLoading
                              ? SizedBox(
                                  height: 20,
                                  width: 20,
                                  child: SpinKitFadingCircle(
                                    color: AppColors.appWhite,
                                    size: 20,
                                  ),
                                )
                              : Text(
                                  'Done',
                                  style: AppTextStyle.access0(
                                      textColor: AppColors.appWhite),
                                ),
                        ),
                      ),
                    ),
                  );
                },
              ),
            )

            // sellerAllOrderActionButton(buttonName:"Done",
            //     colors: AppColors.activeGreen,
            //     onPress: ()async{
            //   //Navigator.of(context).pop();
            //
            //   //Navigator.of(context).pop();
            // }),
            // horizontalSizedBox(10),
            // sellerAllOrderCancelButton(buttonName: "Close",onPress: (){
            //   Navigator.of(context).pop();
            //   // widget.waitingForConfirmationBloc.confirmAndCancelOrder(selectedSubOrders: CommonMethods.sellerSelectedSubOrderList(widget.suborderList));
            // })
          ],
        )
      ],
    );
  }
}
