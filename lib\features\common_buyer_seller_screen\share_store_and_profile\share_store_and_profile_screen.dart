import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:provider/provider.dart';
import 'package:pretty_qr_code/pretty_qr_code.dart';
import 'package:swadesic/features/common_buyer_seller_screen/share_store_and_profile/share_store_and_profile_bloc.dart';
import 'package:swadesic/features/data_model/logged_in_user_info_data_model/logged_in_user_info_data_model.dart';
import 'package:swadesic/features/data_model/seller_own_store_info_data_model/seller_own_store_info_data_model.dart';
import 'package:swadesic/features/data_model/store_dashboard_data_model/store_dashboard_data_model.dart';
import 'package:swadesic/features/widgets/custom_image_container/custom_image_container.dart';
import 'package:swadesic/model/store_info/store_info.dart';
import 'package:swadesic/model/user_details_response/user_details_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

class ShareStoreAndProfileScreen extends StatefulWidget {
  final UserDetail? userDetail;
  final StoreInfo? storeInfo;
  final int initialTab;

  const ShareStoreAndProfileScreen({
    super.key,
    this.storeInfo,
    this.userDetail,
    this.initialTab = 0,
  });

  @override
  State<ShareStoreAndProfileScreen> createState() =>
      _ShareStoreAndProfileScreenState();
}

class _ShareStoreAndProfileScreenState extends State<ShareStoreAndProfileScreen>
    with SingleTickerProviderStateMixin {
  late ShareStoreAndProfileBloc shareStoreAndProfileBloc;
  late TabController _tabController;

  @override
  void initState() {
    shareStoreAndProfileBloc = ShareStoreAndProfileBloc(context);
    shareStoreAndProfileBloc.init();
    _tabController = TabController(
      length: 2,
      vsync: this,
      initialIndex: widget.initialTab,
    );
    super.initState();
  }

  @override
  void dispose() {
    shareStoreAndProfileBloc.dispose();
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.appWhite,
      appBar: AppBar(
        backgroundColor: AppColors.appWhite,
        iconTheme: IconThemeData(color: AppColors.appBlack),
        elevation: 0,
        toolbarHeight: 32,
        bottom: TabBar(
          controller: _tabController,
          tabs: [
            Tab(
              child: Text(
                'Share Profile',
                style: AppTextStyle.access0(
                  textColor: _tabController.index == 0
                      ? AppColors.appBlack
                      : AppColors.writingBlack1,
                ),
              ),
            ),
            Tab(
              child: Text(
                'QR Code',
                style: AppTextStyle.access0(
                  textColor: _tabController.index == 1
                      ? AppColors.appBlack
                      : AppColors.writingBlack1,
                ),
              ),
            ),
          ],
          labelColor: AppColors.appBlack,
          unselectedLabelColor: AppColors.writingBlack1,
          indicatorColor: AppColors.appBlack,
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildShareProfileTab(),
          _buildQRCodeTab(),
        ],
      ),
    );
  }

  Widget _buildShareProfileTab() {
    return Stack(
      alignment: Alignment.center,
      children: [
        Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            CustomImageContainer(
              width: 250,
              height: 250,
              imageUrl: widget.userDetail?.icon ?? widget.storeInfo?.icon,
              imageType: widget.userDetail?.userReference == null
                  ? CustomImageContainerType.store
                  : CustomImageContainerType.user,
            ),
            verticalSizedBox(20),
            Text(
              "@${widget.userDetail?.userName ?? widget.storeInfo?.storehandle}",
              style: AppTextStyle.pageHeading(textColor: AppColors.appBlack),
            ),
          ],
        ),
        Positioned(
          bottom: 20,
          child: _buildInviteFriendsButton(),
        ),
      ],
    );
  }

  Widget _buildQRCodeTab() {
    final String handle =
        widget.userDetail?.userName ?? widget.storeInfo?.storehandle ?? '';
    final String qrData = "${AppConstants.domainName}$handle";

    return Stack(
      alignment: Alignment.center,
      children: [
        SingleChildScrollView(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Padding(
                padding: const EdgeInsets.all(44.0),
                child: Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(24),
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.writingColor2.withOpacity(0.15),
                        spreadRadius: 2,
                        blurRadius: 8,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: Stack(
                    alignment: Alignment.center,
                    children: [
                      // QR Code with embedded square
                      PrettyQrView.data(
                        data: qrData,
                        errorCorrectLevel: QrErrorCorrectLevel.H,
                        decoration: PrettyQrDecoration(
                          shape: PrettyQrSmoothSymbol(
                            color: Colors.black,
                            roundFactor: 1,
                          ),
                          // Create a white square in the middle of the QR code
                          image: PrettyQrDecorationImage(
                            image: AssetImage(
                                'assets/common_images/white_square.png'),
                            position: PrettyQrDecorationImagePosition.embedded,
                            scale: 0.3,
                          ),
                        ),
                      ),
                      // Custom store icon in the center
                      SizedBox(
                        width: 70,
                        height: 70,
                        child: widget.storeInfo?.icon != null
                            ? CustomImageContainer(
                                width: 55,
                                height: 55,
                                imageUrl: widget.storeInfo!.icon,
                                imageType: CustomImageContainerType.store,
                                showShadow: false,
                              )
                            : Image.asset(
                                'assets/common_images/app_icon_png.png',
                                width: 55,
                                height: 55,
                                fit: BoxFit.cover,
                              ),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 20),
              if (widget.storeInfo != null)
                Text(
                  'Your support means the world to us! Join our @$handle community today.',
                  textAlign: TextAlign.center,
                  style:
                      AppTextStyle.pageHeading2(textColor: AppColors.appBlack),
                ),
            ],
          ),
        ),
        Positioned(
          bottom: 20,
          child: _buildInviteFriendsButton(),
        ),
      ],
    );
  }

  Widget _buildInviteFriendsButton() {
    return Consumer<StoreDashboardDataModel>(
      builder: (BuildContext context, data, Widget? child) {
        return CupertinoButton(
          padding: EdgeInsets.zero,
          onPressed: () async {
            String url = "";
            if (widget.userDetail?.userName != null) {
              url = "${AppConstants.domainName}${widget.userDetail?.userName}";
            }

            String inviteCode = "";
            if (!CommonMethods().isStaticUser()) {
              if (AppConstants.appData.isUserView!) {
                LoggedInUserInfoDataModel userDetailDataModel =
                    Provider.of<LoggedInUserInfoDataModel>(
                        AppConstants.currentSelectedTabContext,
                        listen: false);
                inviteCode = userDetailDataModel.userDetail!.inviteCode!;
                if (inviteCode != "") {
                  url = "$url/?ic=$inviteCode";
                }
              } else {
                SellerOwnStoreInfoDataModel sellerOwnStoreInfoDataModel =
                    Provider.of<SellerOwnStoreInfoDataModel>(
                        AppConstants.currentSelectedTabContext,
                        listen: false);
                inviteCode = sellerOwnStoreInfoDataModel.storeInfo!.inviteCode!;
                if (inviteCode != "") {
                  url = "$url/?ic=$inviteCode";
                }
              }
            }

            if (widget.storeInfo?.storeReference != null &&
                (widget.storeInfo?.storeReference ==
                    AppConstants.appData.storeReference) &&
                !data.storeDashBoard.isActive!) {
              return CommonMethods.toastMessage(
                  AppStrings.beforeStoreActivationStoreLink, context);
            }

            widget.storeInfo?.storehandle != null
                ? shareStoreAndProfileBloc.onTapShareStore(
                    storeInfo: widget.storeInfo!)
                : null;
            widget.userDetail?.userName != null
                ? CommonMethods.share(url)
                : null;
          },
          child: Container(
            padding: const EdgeInsets.all(20),
            alignment: Alignment.center,
            width: MediaQuery.of(context).size.width,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                SvgPicture.asset(
                  AppImages.chainIcon,
                  color: AppColors.brandBlack,
                ),
                horizontalSizedBox(10),
                Flexible(
                  child: Text(
                    widget.userDetail?.userName == null
                        ? AppStrings.inviteYourFriendsAndCustomers
                        : AppStrings.shareProfile,
                    maxLines: 2,
                    style:
                        AppTextStyle.subTitle(textColor: AppColors.brandBlack),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
