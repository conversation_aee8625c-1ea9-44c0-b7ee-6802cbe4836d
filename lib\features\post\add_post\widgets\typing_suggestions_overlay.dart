import 'package:flutter/material.dart';
import 'package:swadesic/features/widgets/custom_image_container/custom_image_container.dart';
import 'package:swadesic/model/typing_suggestions_response/typing_suggestions_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:swadesic/services/typing_suggestions_service/typing_suggestions_service.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/common_methods.dart';

enum OverlayState {
  suggestions,
  storeOptions,
  storeProducts,
}

class TypingSuggestionsOverlay extends StatefulWidget {
  final List<SuggestionItem> suggestions;
  final Function(SuggestionItem) onSuggestionTap;
  final bool isLoading;
  final VoidCallback? onLoadMore;
  final bool hasMore;

  const TypingSuggestionsOverlay({
    super.key,
    required this.suggestions,
    required this.onSuggestionTap,
    this.isLoading = false,
    this.onLoadMore,
    this.hasMore = false,
  });

  @override
  State<TypingSuggestionsOverlay> createState() => _TypingSuggestionsOverlayState();
}

class _TypingSuggestionsOverlayState extends State<TypingSuggestionsOverlay> {
  OverlayState currentState = OverlayState.suggestions;
  SuggestionItem? selectedStore;
  List<SuggestionItem> storeProducts = [];
  bool isLoadingProducts = false;
  bool hasMoreProducts = false;
  int productsOffset = 0;
  final int productsLimit = 10;
  final TypingSuggestionsService _service = TypingSuggestionsService();

  @override
  Widget build(BuildContext context) {
    if (widget.suggestions.isEmpty && !widget.isLoading && currentState == OverlayState.suggestions) {
      return const SizedBox.shrink();
    }

    // Get screen height for 50% height calculation
    double screenHeight = MediaQuery.of(context).size.height;
    double overlayHeight = screenHeight * 0.5;

    return Container(
      width: double.infinity, // Full screen width
      height: overlayHeight, // 50% of screen height
      // decoration: BoxDecoration(
      //   color: AppColors.appWhite,
      //   boxShadow: [
      //     BoxShadow(
      //       color: AppColors.appBlack.withOpacity(0.1),
      //       blurRadius: 8,
      //       offset: const Offset(0, 2),
      //     ),
      //   ],
      // ),
      child: _buildCurrentStateContent(),
    );
  }

  Widget _buildCurrentStateContent() {
    switch (currentState) {
      case OverlayState.suggestions:
        return _buildSuggestionsContent();
      case OverlayState.storeOptions:
        return _buildStoreOptionsContent();
      case OverlayState.storeProducts:
        return _buildStoreProductsContent();
    }
  }

  Widget _buildSuggestionsContent() {
    // Check if all suggestions are products to show the title
    bool isShowingProducts = widget.suggestions.isNotEmpty &&
        widget.suggestions.every((suggestion) => suggestion.type == 'PRODUCT');

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Title section (only show for products)
        if (isShowingProducts) ...[
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: AppColors.lightGreen.withOpacity(0.1),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.store,
                  size: 20,
                  color: AppColors.lightGreen,
                ),
                const SizedBox(width: 8),
                Text(
                  'Store Products',
                  style: AppTextStyle.contentHeading0(
                    textColor: AppColors.lightGreen,
                  ).copyWith(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
          Container(
            height: 1,
            color: AppColors.textFieldFill0,
          ),
        ],
        // Suggestions list
        Expanded(
          child: ListView.builder(
            itemCount: widget.suggestions.length + (widget.isLoading ? 1 : 0) + (widget.hasMore ? 1 : 0),
            itemBuilder: (context, index) {
              if (index < widget.suggestions.length) {
                return _buildSuggestionItem(widget.suggestions[index]);
              } else if (widget.isLoading) {
                return _buildLoadingItem();
              } else if (widget.hasMore) {
                return _buildLoadMoreItem();
              }
              return const SizedBox.shrink();
            },
          ),
        ),
      ],
    );
  }

  Widget _buildStoreOptionsContent() {
    if (selectedStore == null) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header with back button
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            color: AppColors.textFieldFill1,
          ),
          child: Row(
            children: [
              IconButton(
                onPressed: _goBackToSuggestions,
                icon: Icon(
                  Icons.arrow_back,
                  color: AppColors.appBlack,
                ),
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(),
              ),
              const SizedBox(width: 8),
              Text(
                'Mention Store',
                style: AppTextStyle.contentHeading0(
                  textColor: AppColors.appBlack,
                ).copyWith(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),

        // Store info
        Container(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              CustomImageContainer(
                width: 42,
                height: 42,
                imageUrl: selectedStore!.imageUrl,
                imageType: CustomImageContainerType.store,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      selectedStore!.primaryText ?? '',
                      style: AppTextStyle.contentHeading0(
                        textColor: AppColors.appBlack,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    if (selectedStore!.secondaryText != null)
                      Text(
                        selectedStore!.secondaryText!,
                        style: AppTextStyle.contentText0(
                          textColor: AppColors.writingBlack1,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                  ],
                ),
              ),
            ],
          ),
        ),

        // Options
        Expanded(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Column(
              children: [
                _buildStoreOption(
                  'Mention Store Only',
                  'Mention the store directly',
                  Icons.store,
                  () => _onStoreOptionSelected(false),
                ),
                const SizedBox(height: 12),
                _buildStoreOption(
                  'Mention Store Products',
                  'Choose a specific product from this store',
                  Icons.inventory,
                  () => _onStoreOptionSelected(true),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildStoreProductsContent() {
    if (selectedStore == null) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header with back button
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            color: AppColors.textFieldFill1,
          ),
          child: Row(
            children: [
              IconButton(
                onPressed: _goBackToStoreOptions,
                icon: Icon(
                  Icons.arrow_back,
                  color: AppColors.appBlack,
                ),
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(),
              ),
              const SizedBox(width: 8),
              Text(
                'Select Product',
                style: AppTextStyle.contentHeading0(
                  textColor: AppColors.appBlack,
                ).copyWith(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),

        // Store info
        Container(
          padding: const EdgeInsets.all(12),
          margin: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: AppColors.textFieldFill1,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            children: [
              CustomImageContainer(
                width: 30,
                height: 30,
                imageUrl: selectedStore!.imageUrl,
                imageType: CustomImageContainerType.store,
              ),
              const SizedBox(width: 8),
              Text(
                selectedStore!.primaryText ?? '',
                style: AppTextStyle.contentText0(
                  textColor: AppColors.appBlack,
                ),
              ),
            ],
          ),
        ),

        // Products list
        Expanded(
          child: _buildProductsList(),
        ),
      ],
    );
  }

  Widget _buildSuggestionItem(SuggestionItem suggestion) {
    return InkWell(
      onTap: () => _onSuggestionTap(suggestion),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        child: Row(
          children: [
            // Profile image
            CustomImageContainer(
              width: 42,
              height: 42,
              imageUrl: suggestion.imageUrl,
              imageType: _getImageType(suggestion.type),
            ),
            const SizedBox(width: 12),
            // Text content
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    suggestion.primaryText ?? '',
                    style: AppTextStyle.contentHeading0(
                      textColor: AppColors.appBlack,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  if (suggestion.secondaryText != null &&
                      suggestion.secondaryText!.isNotEmpty)
                    Text(
                      suggestion.secondaryText!,
                      style: AppTextStyle.contentText0(
                        textColor: AppColors.writingBlack1,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                ],
              ),
            ),
            // Type indicator
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: _getTypeColor(suggestion.type).withOpacity(0.1),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Text(
                _getTypeLabel(suggestion.type),
                style: AppTextStyle.contentText0(
                  textColor: _getTypeColor(suggestion.type),
                ).copyWith(fontSize: 10),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStoreOption(
    String title,
    String subtitle,
    IconData icon,
    VoidCallback onTap,
  ) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          border: Border.all(color: AppColors.textFieldFill0),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            Icon(
              icon,
              color: AppColors.brandBlack,
              size: 24,
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: AppTextStyle.contentHeading0(
                      textColor: AppColors.appBlack,
                    ),
                  ),
                  Text(
                    subtitle,
                    style: AppTextStyle.contentText0(
                      textColor: AppColors.writingBlack1,
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              color: AppColors.writingBlack1,
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProductsList() {
    if (isLoadingProducts && storeProducts.isEmpty) {
      return AppCommonWidgets.appCircularProgress();
    }

    if (storeProducts.isEmpty && !isLoadingProducts) {
      return Center(
        child: Text(
          'No products found',
          style: AppTextStyle.contentText0(
            textColor: AppColors.writingBlack1,
          ),
        ),
      );
    }

    return NotificationListener<ScrollNotification>(
      onNotification: (ScrollNotification scrollInfo) {
        // Check if user has scrolled to near the bottom (85% of the way)
        // Also check if we're close to the bottom (within 200 pixels)
        bool nearBottom = scrollInfo.metrics.pixels >= scrollInfo.metrics.maxScrollExtent * 0.85;
        bool closeToBottom = scrollInfo.metrics.maxScrollExtent - scrollInfo.metrics.pixels <= 200;

        if ((nearBottom || closeToBottom) && scrollInfo.metrics.maxScrollExtent > 0) {
          // Load more products if available and not already loading
          if (hasMoreProducts && !isLoadingProducts) {
            _loadProducts(loadMore: true);
          }
        }
        return false;
      },
      child: ListView.builder(
        itemCount: storeProducts.length + (isLoadingProducts && storeProducts.isNotEmpty ? 1 : 0),
        itemBuilder: (context, index) {
          if (index < storeProducts.length) {
            return _buildProductItem(storeProducts[index]);
          } else {
            // Show loading indicator at the bottom when loading more
            return _buildPaginationLoadingItem();
          }
        },
      ),
    );
  }

  Widget _buildProductItem(SuggestionItem product) {
    return InkWell(
      onTap: () => _onProductSelected(product),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        child: Row(
          children: [
            CustomImageContainer(
              width: 40,
              height: 40,
              imageUrl: product.imageUrl,
              imageType: CustomImageContainerType.product,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    product.primaryText ?? '',
                    style: AppTextStyle.contentHeading0(
                      textColor: AppColors.appBlack,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  if (product.secondaryText != null)
                    Text(
                      product.secondaryText!,
                      style: AppTextStyle.contentText0(
                        textColor: AppColors.writingBlack1,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPaginationLoadingItem() {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: SizedBox(
          height: 20,
          width: 20,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(AppColors.brandGreen),
          ),
        ),
      ),
    );
  }

  Widget _buildLoadingItem() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: const Center(
        child: SizedBox(
          height: 20,
          width: 20,
          child: CircularProgressIndicator(strokeWidth: 2),
        ),
      ),
    );
  }

  Widget _buildLoadMoreItem() {
    return InkWell(
      onTap: widget.onLoadMore,
      child: Container(
        padding: const EdgeInsets.all(16),
        child: Center(
          child: Text(
            'Load more...',
            style: AppTextStyle.contentText0(
              textColor: AppColors.brandBlack,
            ),
          ),
        ),
      ),
    );
  }

  // Navigation methods
  void _onSuggestionTap(SuggestionItem suggestion) {
    if (suggestion.type == 'STORE') {
      setState(() {
        selectedStore = suggestion;
        currentState = OverlayState.storeOptions;
      });
    } else {
      widget.onSuggestionTap(suggestion);
    }
  }

  void _onStoreOptionSelected(bool showProducts) {
    if (showProducts) {
      setState(() {
        currentState = OverlayState.storeProducts;
      });
      _loadProducts();
    } else {
      widget.onSuggestionTap(selectedStore!);
    }
  }

  void _onProductSelected(SuggestionItem product) {
    widget.onSuggestionTap(product);
  }

  void _goBackToSuggestions() {
    setState(() {
      currentState = OverlayState.suggestions;
      selectedStore = null;
    });
  }

  void _goBackToStoreOptions() {
    setState(() {
      currentState = OverlayState.storeOptions;
      storeProducts.clear();
    });
  }

  // Product loading methods
  Future<void> _loadProducts({bool loadMore = false}) async {
    if (selectedStore == null) return;

    // Prevent multiple simultaneous requests
    if (isLoadingProducts) return;

    if (!loadMore) {
      setState(() {
        isLoadingProducts = true;
        productsOffset = 0;
        storeProducts.clear();
      });
    } else {
      setState(() {
        isLoadingProducts = true;
      });
    }

    try {
      String query = "@${selectedStore!.primaryText}/";
      String visitorReference = AppConstants.appData.isUserView!
          ? AppConstants.appData.userReference!
          : AppConstants.appData.storeReference!;

      TypingSuggestionsResponse response = await _service.getTypingSuggestions(
        query: query,
        limit: productsLimit,
        offset: productsOffset,
        visitorReference: visitorReference,
        userPincode: AppConstants.appData.pinCode,
      );

      if (mounted) {
        setState(() {
          if (loadMore) {
            storeProducts.addAll(response.results ?? []);
          } else {
            storeProducts = response.results ?? [];
          }
          hasMoreProducts = (response.results?.length ?? 0) == productsLimit;
          productsOffset += productsLimit;
          isLoadingProducts = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          isLoadingProducts = false;
        });
        CommonMethods.toastMessage('Failed to load products', context);
      }
    }
  }

  CustomImageContainerType _getImageType(String? type) {
    switch (type) {
      case 'USER':
        return CustomImageContainerType.user;
      case 'STORE':
        return CustomImageContainerType.store;
      case 'PRODUCT':
        return CustomImageContainerType.product;
      default:
        return CustomImageContainerType.user;
    }
  }

  Color _getTypeColor(String? type) {
    switch (type) {
      case 'USER':
        return AppColors.orange;
      case 'STORE':
        return AppColors.brandGreen;
      case 'PRODUCT':
        return AppColors.lightGreen;
      default:
        return AppColors.writingBlack1;
    }
  }

  String _getTypeLabel(String? type) {
    switch (type) {
      case 'USER':
        return 'User';
      case 'STORE':
        return 'Store';
      case 'PRODUCT':
        return 'Product';
      default:
        return '';
    }
  }
}
