import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/data_model/notification_data_mdel/user_or_store_notification.dart';
import 'package:swadesic/features/notification/notification_bloc.dart';
import 'package:swadesic/features/notification/notification_common_widget/notification_common_widget.dart';
import 'package:swadesic/features/notification/user_or_store_notification/user_or_store_notification_bloc.dart';
import 'package:swadesic/features/widgets/no_result/no_result.dart';
import 'package:swadesic/model/notification_response/notification_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:swadesic/util/auto_hide_navigation_service.dart';

//region User or store notification
class UserOrStoreNotification extends StatefulWidget {
  final Function(int) unSeenCount;
  const UserOrStoreNotification({super.key, required this.unSeenCount});

  @override
  State<UserOrStoreNotification> createState() =>
      _UserOrStoreNotificationState();
}
//endregion

class _UserOrStoreNotificationState extends State<UserOrStoreNotification>
    with
        AutomaticKeepAliveClientMixin<UserOrStoreNotification>,
        AutoHideNavigationMixin<UserOrStoreNotification> {
  //Keep alive
  @override
  bool get wantKeepAlive => true;

  //User or store notification bloc
  late UserOrStoreNotificationBloc userOrStoreNotificationBloc;

  //endregion

  //region Init
  @override
  void initState() {
    userOrStoreNotificationBloc =
        UserOrStoreNotificationBloc(context, widget.unSeenCount);
    userOrStoreNotificationBloc.init();

    // Enable auto-hide navigation and attach scroll controller
    enableAutoHideNavigation();
    attachScrollControllerToAutoHide(
        userOrStoreNotificationBloc.scrollController);

    super.initState();
  }
  //endregion

  @override
  Widget build(BuildContext context) {
    return body();
  }

  //region Body
  Widget body() {
    return StreamBuilder<UserOrStoreNotificationState>(
        stream: userOrStoreNotificationBloc.userOrStoreNotificationCtrl.stream,
        initialData: UserOrStoreNotificationState.Loading,
        builder: (context, snapshot) {
          if (snapshot.data == UserOrStoreNotificationState.Success) {
            return RefreshIndicator(
              color: AppColors.brandBlack,
              onRefresh: () async {
                await userOrStoreNotificationBloc.getUserOrStoreNotification();
              },
              child: ListView.separated(
                physics: const BouncingScrollPhysics(),
                controller: userOrStoreNotificationBloc.scrollController,
                separatorBuilder: (context, index) =>
                    const SizedBox(height: 20),
                padding:
                    const EdgeInsets.symmetric(horizontal: 15, vertical: 20),
                // shrinkWrap: true,
                itemCount: userOrStoreNotificationBloc
                    .userOrStoreNotificationResponse.notifications!.length,
                itemBuilder: (context, index) {
                  //print("Total personal notification are ${userOrStoreNotificationBloc.userOrStoreNotificationResponse.notifications!.length}");
                  return NotificationCommonWidget.notificationCard(
                    onTap: () async {
                      userOrStoreNotificationBloc.updateNotification(
                          notificationDetail: userOrStoreNotificationBloc
                              .userOrStoreNotificationResponse
                              .notifications![index]);
                    },
                    notificationDetail: userOrStoreNotificationBloc
                        .userOrStoreNotificationResponse.notifications![index],
                    context: context,
                  );
                },
              ),
            );
          }
          if (snapshot.data == UserOrStoreNotificationState.Empty) {
            return RefreshIndicator(
              color: AppColors.brandBlack,
              onRefresh: () async {
                await userOrStoreNotificationBloc.getUserOrStoreNotification();
              },
              child: LayoutBuilder(builder: (context, constraints) {
                return SingleChildScrollView(
                  physics: const AlwaysScrollableScrollPhysics(),
                  child: Container(
                    alignment: Alignment.center,
                    height: constraints.maxHeight > 0
                        ? constraints.maxHeight
                        : MediaQuery.of(context).size.height,
                    child: Center(
                        child: NoResult(message: AppStrings.noNotificationYet)),
                  ),
                );
              }),
            );
          }
          if (snapshot.data == UserOrStoreNotificationState.Failed) {
            return AppCommonWidgets.errorWidget(onTap: () {
              userOrStoreNotificationBloc.getUserOrStoreNotification();
            });
          }
          if (snapshot.data == UserOrStoreNotificationState.Loading) {
            return AppCommonWidgets.appCircularProgress();
          }
          return const SizedBox();
        });
  }
  //endregion

//
//   //region Notification List
//   Widget notificationList() {
//     return SizedBox(
//       height: MediaQuery.of(context).size.height,
//       child: RefreshIndicator(
//         onRefresh: () async{
//          await userOrStoreNotificationBloc.getUserOrStoreNotification();
//         },
//         color: AppColors.brandGreen,
//         child: ListView(
//           padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 20),
//           // shrinkWrap: true,
//           children: [
//             today(),
//             yesterday(),
//             thisWeek(),
//             pastWeek(),
//           ],
//         ),
//       ),
//     );
//   }
//
//   //endregion
//
//   //region Today
//   Widget today() {
//     if (userOrStoreNotificationBloc.userOrStoreNotificationDataModel.userOrStoreNotificationResponse!.today!.isNotEmpty) {
//       return Column(
//         mainAxisSize: MainAxisSize.min,
//         mainAxisAlignment: MainAxisAlignment.start,
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           Text(
//             "Today",
//             style: AppTextStyle.contentHeading0(textColor: AppColors.writingBlack1),
//           ),
//           ListView.builder(
//             padding: const EdgeInsets.only(top: 20),
//             physics: const NeverScrollableScrollPhysics(),
//             shrinkWrap: true,
//             itemCount: userOrStoreNotificationBloc.userOrStoreNotificationDataModel.userOrStoreNotificationResponse!.today!.length,
//             itemBuilder: (context, index) {
//               return NotificationCommonWidget.notificationCard(
//                 onTap: () {
//                   userOrStoreNotificationBloc.updateNotification(notificationDetail: userOrStoreNotificationBloc.userOrStoreNotificationDataModel.userOrStoreNotificationResponse!.today![index]);
//                 },
//                 notificationDetail: userOrStoreNotificationBloc.userOrStoreNotificationDataModel.userOrStoreNotificationResponse!.today![index],
//                 context: context,
//               );
//             },
//           ),
//         ],
//       );
//     } else {
//       return const SizedBox();
//     }
//   }
//
//   //endregion
//
//   //region Yesterday
//   Widget yesterday() {
//     if (userOrStoreNotificationBloc.userOrStoreNotificationDataModel.userOrStoreNotificationResponse!.yesterday!.isNotEmpty) {
//       return Column(
//         mainAxisSize: MainAxisSize.min,
//         mainAxisAlignment: MainAxisAlignment.start,
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           userOrStoreNotificationBloc.userOrStoreNotificationDataModel.userOrStoreNotificationResponse!.yesterday!.isEmpty
//               ? const SizedBox()
//               : Text(
//             "Yesterday",
//             style: AppTextStyle.contentHeading0(textColor: AppColors.writingBlack1),
//           ),
//           ListView.builder(
//             padding: const EdgeInsets.only(top: 20),
//             physics: const NeverScrollableScrollPhysics(),
//             shrinkWrap: true,
//             itemCount: userOrStoreNotificationBloc.userOrStoreNotificationDataModel.userOrStoreNotificationResponse!.yesterday!.length,
//             itemBuilder: (context, index) {
//               return NotificationCommonWidget.notificationCard(
//                 onTap: () {
//                   userOrStoreNotificationBloc.updateNotification(notificationDetail: userOrStoreNotificationBloc.userOrStoreNotificationDataModel.userOrStoreNotificationResponse!.yesterday![index]);
//                 },
//                 notificationDetail: userOrStoreNotificationBloc.userOrStoreNotificationDataModel.userOrStoreNotificationResponse!.yesterday![index],
//                 context: context,
//               );
//             },
//           ),
//         ],
//       );
//     } else {
//       return const SizedBox();
//     }
//   }
//
//   //endregion
//
//   //region This Week
//   Widget thisWeek() {
//     // Retrieve the data from the notification
//     // UserOrStoreNotification notificationDataModel = Provider.of<UserOrStoreNotification>(context);
//
//     if (userOrStoreNotificationBloc.userOrStoreNotificationDataModel.userOrStoreNotificationResponse!.thisWeek!.isNotEmpty) {
//       return Column(
//         mainAxisSize: MainAxisSize.min,
//         mainAxisAlignment: MainAxisAlignment.start,
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           userOrStoreNotificationBloc.userOrStoreNotificationDataModel.userOrStoreNotificationResponse!.thisWeek!.isEmpty
//               ? const SizedBox()
//               : Text(
//             "Last 7 days",
//             style: AppTextStyle.contentHeading0(textColor: AppColors.writingBlack1),
//           ),
//           ListView.builder(
//             padding: const EdgeInsets.only(top: 20),
//             physics: const NeverScrollableScrollPhysics(),
//             shrinkWrap: true,
//             itemCount: userOrStoreNotificationBloc.userOrStoreNotificationDataModel.userOrStoreNotificationResponse!.thisWeek!.length,
//             itemBuilder: (context, index) {
//
//               return NotificationCommonWidget.notificationCard(
//                 onTap: () {
//                   userOrStoreNotificationBloc.updateNotification(notificationDetail: userOrStoreNotificationBloc.userOrStoreNotificationDataModel.userOrStoreNotificationResponse!.thisWeek![index]);
//                 },
//                 notificationDetail: userOrStoreNotificationBloc.userOrStoreNotificationDataModel.userOrStoreNotificationResponse!.thisWeek![index],
//                 context: context,
//               );
//             },
//           ),
//         ],
//       );
//     } else {
//       return const SizedBox();
//     }
//   }
//
//   //endregion
//
//   //region Past week
//   Widget pastWeek() {
//     // Retrieve the data from the notification
//     // UserOrStoreNotification notificationDataModel = Provider.of<UserOrStoreNotification>(context);
//
//     if (userOrStoreNotificationBloc.userOrStoreNotificationDataModel.userOrStoreNotificationResponse!.pastWeek!.isNotEmpty) {
//       return Column(
//         mainAxisSize: MainAxisSize.min,
//         mainAxisAlignment: MainAxisAlignment.start,
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           userOrStoreNotificationBloc.userOrStoreNotificationDataModel.userOrStoreNotificationResponse!.pastWeek!.isEmpty
//               ? const SizedBox()
//               : Padding(
//             padding: const EdgeInsets.all(10),
//             child: Text(
//               "Past",
//               style: AppTextStyle.contentHeading0(textColor: AppColors.writingBlack1),
//             ),
//           ),
//           ListView.builder(
//             padding: const EdgeInsets.only(top: 20),
//             physics: const NeverScrollableScrollPhysics(),
//             shrinkWrap: true,
//             itemCount: userOrStoreNotificationBloc.userOrStoreNotificationDataModel.userOrStoreNotificationResponse!.pastWeek!.length,
//             itemBuilder: (context, index) {
//
//               return NotificationCommonWidget.notificationCard(
//                 onTap: () {
//                   userOrStoreNotificationBloc.updateNotification(notificationDetail: userOrStoreNotificationBloc.userOrStoreNotificationDataModel.userOrStoreNotificationResponse!.pastWeek![index]);
//                 },
//                 notificationDetail: userOrStoreNotificationBloc.userOrStoreNotificationDataModel.userOrStoreNotificationResponse!.pastWeek![index],
//                 context: context,
//               );
//               // return notificationCard(notificationDetail: notificationDataModel.getNotificationResponse!.pastWeek![index]);
//             },
//           ),
//         ],
//       );
//     } else {
//       return const SizedBox();
//     }
//   }
//
// //endregion

  //region Dispose
  @override
  void dispose() {
    // Detach scroll controller and disable auto-hide navigation
    detachScrollControllerFromAutoHide(
        userOrStoreNotificationBloc.scrollController);
    disableAutoHideNavigation();

    super.dispose();
  }
  //endregion
}
