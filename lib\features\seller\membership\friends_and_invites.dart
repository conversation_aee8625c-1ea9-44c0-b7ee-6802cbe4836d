import 'dart:ui';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:lottie/lottie.dart';
import 'package:swadesic/features/buyers/buyer_home/buyer_home_bloc.dart';
import 'package:swadesic/features/seller/membership/membership_bloc.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_widgets.dart';

class FriendsAndInvites extends StatefulWidget {
  final MembershipBloc membershipBloc;

  const FriendsAndInvites({Key? key, required this.membershipBloc})
      : super(key: key);

  @override
  State<FriendsAndInvites> createState() => _FriendsAndInvitesState();
}

class _FriendsAndInvitesState extends State<FriendsAndInvites> {
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        searchFilter(),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              ///If user rank is 3
              Visibility(
                visible: AppConstants.userMemberRank == 3,
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Expanded(child: inviteLeft(true)),
                    horizontalSizedBox(10),
                    Expanded(child: inviteLeft(false)),
                  ],
                ),
              ),

              ///If user rank is 2
              Visibility(
                visible: AppConstants.userMemberRank == 2,
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Expanded(child: inviteLeft(true)),
                  ],
                ),
              ),

              ///Hide if user rank is 1
              Visibility(
                  visible: AppConstants.userMemberRank == 1 ? false : true,
                  child: sendUsingText()),
            ],
          ),
        ),
        Expanded(
          child: StreamBuilder<MembershipState>(
              stream: widget.membershipBloc.friendsInviteCtrl.stream,
              // initialData: MembershipState.Loading,
              builder: (context, snapshot) {
                if (snapshot.data == MembershipState.Loading) {
                  return Center(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        SizedBox(
                          height: 100,
                          width: 100,
                          child: Lottie.asset(AppImages.pullingContact),
                          // child: Lottie.network("https://assets8.lottiefiles.com/packages/lf20_a2chheio.json"),
                        ),
                        AppCommonWidgets.emptyResponseText(
                          emptyMessage: AppStrings.weArePulling,
                        )
                      ],
                    ),
                    // child: appText(AppStrings.weArePulling),
                  );
                }
                if (snapshot.data == MembershipState.Empty) {
                  return Column(
                    children: [
                      verticalSizedBox(10),
                      findFriend(),
                      Expanded(
                          child: Center(
                              child: AppCommonWidgets.emptyResponseText(
                        emptyMessage: AppStrings.thatIsStrange,
                      ))),
                    ],
                  );
                }
                if (snapshot.data == MembershipState.NoPermission) {
                  return Column(
                    children: [
                      verticalSizedBox(10),
                      findFriend(),
                      Expanded(
                          child: Center(
                              child: AppCommonWidgets.emptyResponseText(
                        emptyMessage: AppStrings.pleaseSendInviteUsing,
                      ))),
                    ],
                  );
                }
                if (snapshot.data == MembershipState.Success) {
                  return Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 10),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        //memberSellerLeft(),

                        widget.membershipBloc.friendsInvitesResponse.message ==
                                null
                            ? verticalSizedBox(0)
                            : contactNameNumber(),
                      ],
                    ),
                  );
                }
                return verticalSizedBox(0);
              }),
        ),
      ],
    );
  }

  // children: [
  // searchFilter(),
  // contactNameNumber(),
  // ],

  //region Search and filter
  Widget searchFilter() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 10),
      child: SizedBox(
        height: 44,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            Expanded(
              child: TextFormField(
                maxLines: 1,
                controller: widget.membershipBloc.friendsInviteSearchTextCtrl,
                onChanged: (value) {
                  widget.membershipBloc.onChangeFriendInvitesSearchText(value);
                },
                style: TextStyle(
                    fontFamily: "LatoRegular",
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                    color: AppColors.appBlack),
                decoration: InputDecoration(
                  prefixIcon: Padding(
                    padding: const EdgeInsets.all(10),
                    child: SvgPicture.asset(
                      AppImages.searchBarIcon,
                      fit: BoxFit.contain,
                      color: AppColors.appBlack7,
                    ),
                  ),
                  filled: true,
                  contentPadding: const EdgeInsets.symmetric(vertical: 5),
                  fillColor: AppColors.textFieldFill1,
                  isDense: true,
                  hintText: "search people",
                  hintStyle: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w400,
                      color: AppColors.writingColor3),
                  border: InputBorder.none,
                  focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(22),
                      borderSide: BorderSide.none),
                  enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(22),
                      borderSide: BorderSide.none),
                ),
              ),
            ),
            horizontalSizedBox(10),
            InkWell(
              onTap: () {
                widget.membershipBloc.onTapDrawer();
              },
              child: SvgPicture.asset(
                AppImages.filter,
                color: AppColors.appBlack,
                fit: BoxFit.contain,
              ),
            )
          ],
        ),
      ),
    );
  }

//endregion

  //region Member seller left
  Widget memberSellerLeft() {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 10),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          ///If user rank is 3
          Visibility(
            visible: AppConstants.userMemberRank == 3,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Expanded(child: inviteLeft(true)),
                horizontalSizedBox(10),
                Expanded(child: inviteLeft(false)),
              ],
            ),
          ),

          ///If user rank is 2
          Visibility(
            visible: AppConstants.userMemberRank == 2,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Expanded(child: inviteLeft(true)),
              ],
            ),
          ),

          ///Hide if user rank is 1
          Visibility(
              visible: AppConstants.userMemberRank == 1 ? false : true,
              child: sendUsingText()),
        ],
      ),
    );
  }
  //endregion

  ///
//region Contact name and  numbers
  Widget contactNameNumber() {
    // late Iterable<FriendsInfo> gg = [];
    // String userName = "";
    List<String> nameList = [];

    for (var name in widget.membershipBloc.friendsInviteSearch) {
      nameList.add(name.name);
      // gg = widget.membershipBloc.friendsInviteSearch
      //     .where((element) => element.name == name.name);
    }

    nameList = nameList.toSet().toList();

    //print(nameList.toList());
    //print("friends invite search ${widget.membershipBloc.friendsInviteSearch.length}");

    return Expanded(
      child: RefreshIndicator(
        onRefresh: () async {
          widget.membershipBloc.onSelectTab(1);
        },
        child: ListView.builder(
            padding: const EdgeInsets.only(left: 10, right: 10, bottom: 100),
            itemCount: nameList.length,
            itemBuilder: (buildContext, index) {
              return Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  verticalSizedBox(15),
                  Text(
                    widget.membershipBloc.friendsInviteSearch[index].name,
                    // Text(
                    //   "${gg.length}",
                    style: TextStyle(
                      fontFamily: "RobotoRegular",
                      fontSize: 16,
                      fontWeight: FontWeight.w400,
                      color: AppColors.appBlack,
                    ),
                  ),
                  verticalSizedBox(5),
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      //Number
                      Text(
                        widget.membershipBloc.friendsInviteSearch[index]
                            .phonenumber!,
                        style: TextStyle(
                          fontFamily: "LatoRegular",
                          fontSize: 12,
                          fontWeight: FontWeight.w400,
                          color: AppColors.appBlack,
                        ),
                      ),
                      horizontalSizedBox(10),
                      //Member
                      Visibility(
                        visible: widget.membershipBloc
                                .friendsInviteSearch[index].rank ==
                            2,
                        child: Container(
                          alignment: Alignment.center,
                          padding: const EdgeInsets.symmetric(
                              horizontal: 10, vertical: 3),
                          decoration: BoxDecoration(
                              color: AppColors.inActiveGreen,
                              borderRadius:
                                  BorderRadius.all(Radius.circular(20))),
                          child: Text(
                            "member",
                            style: TextStyle(
                              fontFamily: "LatoRegular",
                              fontSize: 12,
                              fontWeight: FontWeight.w400,
                              color: AppColors.appBlack,
                            ),
                          ),
                        ),
                      ),
                      //Seller
                      Visibility(
                        visible: widget.membershipBloc
                                .friendsInviteSearch[index].rank ==
                            3,
                        child: Container(
                          alignment: Alignment.center,
                          padding: const EdgeInsets.symmetric(
                              horizontal: 10, vertical: 3),
                          decoration: BoxDecoration(
                              color: AppColors.purple,
                              borderRadius:
                                  BorderRadius.all(Radius.circular(20))),
                          child: Text(
                            "seller",
                            style: TextStyle(
                              fontFamily: "LatoRegular",
                              fontSize: 12,
                              fontWeight: FontWeight.w400,
                              color: AppColors.appBlack,
                            ),
                          ),
                        ),
                      ),
                      Expanded(child: horizontalSizedBox(10)),

                      ///Share app (Not in use)
                      //Share app
                      // Visibility(
                      //   visible: widget.membershipBloc.friendsInviteSearch[index]
                      //       .userReference == null ,
                      //   // visible: widget.membershipBloc.friendsInviteSearch[index]
                      //   //             .rank ==
                      //   //         1 &&
                      //   //     AppConstants.userMemberRank == 1 ,
                      //   child: InkWell(
                      //     onTap: () {
                      //       CommonMethods.share("https://swadesic.com");
                      //     },
                      //     child: Container(
                      //       alignment: Alignment.center,
                      //       padding:
                      //           EdgeInsets.symmetric(horizontal: 10, vertical: 5),
                      //       decoration: BoxDecoration(
                      //           color: AppColors.lightestGrey,
                      //           borderRadius:
                      //               BorderRadius.all(Radius.circular(20))),
                      //       child: Text(
                      //         "share app",
                      //         style: TextStyle(
                      //           fontFamily: "LatoBold",
                      //           fontSize: 15,
                      //           fontWeight: FontWeight.w700,
                      //           color: AppColors.writingColor2,
                      //         ),
                      //       ),
                      //     ),
                      //   ),
                      // ),
                      ///Request  invite
                      Visibility(
                        visible: widget.membershipBloc
                                .friendsInviteSearch[index].rank >
                            AppConstants.userMemberRank,
                        child: InkWell(
                          onTap: () {
                            widget.membershipBloc.requestSendBottomSheet(
                                "request",
                                widget.membershipBloc.friendsInviteSearch[index]
                                    .rank,
                                widget.membershipBloc.friendsInviteSearch[index]
                                    .phonenumber!,
                                widget.membershipBloc.friendsInviteSearch[index]
                                    .name);
                          },
                          child: Container(
                            alignment: Alignment.center,
                            padding: const EdgeInsets.symmetric(
                                horizontal: 10, vertical: 5),
                            decoration: BoxDecoration(
                                color: AppColors.textFieldFill1,
                                borderRadius:
                                    BorderRadius.all(Radius.circular(20))),
                            child: Text(
                              "request invite",
                              style: TextStyle(
                                fontFamily: "LatoBold",
                                fontSize: 15,
                                fontWeight: FontWeight.w700,
                                color: AppColors.writingColor2,
                              ),
                            ),
                          ),
                        ),
                      ),

                      ///Send invite
                      Visibility(
                        visible: widget.membershipBloc
                                    .friendsInviteSearch[index].rank <
                                AppConstants.userMemberRank &&
                            1 <=
                                widget.membershipBloc.friendsInviteSearch[index]
                                    .rank,

                        ///Commented this line for a reason
                        // && widget.membershipBloc.friendsInviteSearch[index].userReference != null,
                        child: InkWell(
                          onTap: () {
                            ///If already invited
                            if (widget.membershipBloc.friendsInviteSearch[index]
                                    .inviteCodeByThisUser !=
                                null) {
                              widget.membershipBloc.alreadyJoinedVisible(
                                  widget.membershipBloc
                                      .friendsInviteSearch[index],
                                  widget.membershipBloc);
                            }

                            ///Din not invited
                            else {
                              widget.membershipBloc.requestSendBottomSheet(
                                  "send",
                                  widget.membershipBloc
                                      .friendsInviteSearch[index].rank,
                                  widget.membershipBloc
                                      .friendsInviteSearch[index].phonenumber!,
                                  widget.membershipBloc
                                      .friendsInviteSearch[index].name);
                            }
                          },
                          child: Container(
                            alignment: Alignment.center,
                            padding: const EdgeInsets.symmetric(
                                horizontal: 10, vertical: 5),
                            decoration: BoxDecoration(
                                color: AppColors.textFieldFill1,
                                borderRadius:
                                    BorderRadius.all(Radius.circular(20))),
                            child: Text(
                              "send invite",
                              style: TextStyle(
                                fontFamily: "LatoBold",
                                fontSize: 15,
                                fontWeight: FontWeight.w700,
                                color: AppColors.writingColor2,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  )
                ],
              );
            }),
      ),
    );
  }

//endregion

//region Invite left
  Widget inviteLeft(bool isMember) {
    return StreamBuilder<MembershipState>(
        stream: widget.membershipBloc.friendsInviteCtrl.stream,
        initialData: MembershipState.Loading,
        builder: (context, snapshot) {
          return Container(
            width: double.infinity,
            alignment: Alignment.center,
            padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 5),
            decoration: BoxDecoration(
                color: AppColors.textFieldFill1,
                borderRadius: BorderRadius.all(Radius.circular(10))),
            child: Center(
              child: FittedBox(
                child: Text(
                  isMember
                      ? "Member invites left : ${BuyerHomeBloc.userDetailsResponse.userDetail!.memberInviteBalance!}"
                      : "Seller invites left : ${BuyerHomeBloc.userDetailsResponse.userDetail!.sellerInviteBalance!}",
                  style: TextStyle(
                    fontFamily: "LatoSemibold",
                    fontSize: 15,
                    fontWeight: FontWeight.w600,
                    color: AppColors.writingBlack,
                  ),
                ),
              ),
            ),
          );
        });
  }

//endregion

  //region Send invite using phone Text
  Widget sendUsingText() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 10),
      child: CupertinoButton(
        padding: EdgeInsets.zero,
        onPressed: () {
          widget.membershipBloc.onTapInviteUsingPhone();
        },
        child: Text(
          "send invite using phone number",
          style: TextStyle(
            fontFamily: "LatoSemibold",
            fontSize: 15,
            fontWeight: FontWeight.w600,
            color: AppColors.brandBlack,
            decoration: TextDecoration.underline,
          ),
        ),
      ),
    );
  }

//endregion

  //region Find your friend
  Widget findFriend() {
    return Padding(
      padding: const EdgeInsets.only(bottom: 10, left: 10, right: 10),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
        decoration: BoxDecoration(
          color: AppColors.brandBlack,
          borderRadius: const BorderRadius.all(Radius.circular(10)),
          boxShadow: [
            BoxShadow(
              offset: const Offset(0, 1),
              blurRadius: 5,
              color: AppColors.appBlack.withOpacity(0.2),
            ),
          ],
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Expanded(
              child: Text(
                "Find your friends on Whitelabel to know their product experiences",
                maxLines: 2,
                textAlign: TextAlign.start,
                overflow: TextOverflow.visible,
                style: TextStyle(
                    fontSize: 14,
                    color: AppColors.appWhite,
                    fontFamily: "LatoSemibold",
                    fontWeight: FontWeight.w600),
              ),
            ),
            horizontalSizedBox(5),
            SizedBox(
                width: 133,
                height: 51,
                child: ImageFiltered(
                  imageFilter: ImageFilter.blur(sigmaX: 1, sigmaY: 1),
                  child: Image.asset(
                    AppImages.fiendFriend,
                    cacheWidth: 399,
                    cacheHeight: 153,
                  ),
                ))
          ],
        ),
      ),
    );
  }
//endregion
}
