import 'package:flutter/material.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/external_reviews/external_review_request_bloc.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:swadesic/features/widgets/post_widgets/post_image.dart';
import 'package:swadesic/features/widgets/custom_image_container/custom_image_container.dart';

class ExternalReviewRequestScreen extends StatefulWidget {
  final String token;
  final String productReference;
  final String userReference;

  const ExternalReviewRequestScreen({
    Key? key,
    required this.token,
    required this.productReference,
    required this.userReference,
  }) : super(key: key);

  @override
  ExternalReviewRequestScreenState createState() =>
      ExternalReviewRequestScreenState();
}

class ExternalReviewRequestScreenState
    extends State<ExternalReviewRequestScreen> {
  late ExternalReviewRequestBloc bloc;

  @override
  void initState() {
    super.initState();
    bloc = ExternalReviewRequestBloc(
        context, widget.token, widget.productReference, widget.userReference);
  }

  @override
  void dispose() {
    bloc.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: AppColors.appWhite,
        elevation: 0,
        leading: IconButton(
          icon: SvgPicture.asset(AppImages.backButton),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          "You're Invited to Review",
          style: AppTextStyle.heading1Medium(textColor: AppColors.appBlack),
        ),
        actions: [
          IconButton(
            icon: Icon(Icons.more_vert, color: AppColors.appBlack),
            onPressed: () {},
          ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    return StreamBuilder<ExternalReviewState>(
      stream: bloc.stateCtrl.stream,
      initialData: ExternalReviewState.initial,
      builder: (context, snapshot) {
        if (snapshot.data == ExternalReviewState.loading) {
          return Center(child: AppCommonWidgets.appCircularProgress());
        }

        if (snapshot.data == ExternalReviewState.failed) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  'Failed to load product details',
                  style:
                      AppTextStyle.contentText0(textColor: AppColors.appBlack),
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () => Navigator.pop(context),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.brandBlack,
                    padding: const EdgeInsets.symmetric(
                        horizontal: 24, vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: Text(
                    'Go Back',
                    style: AppTextStyle.contentText0(
                        textColor: AppColors.appWhite),
                  ),
                ),
              ],
            ),
          );
        }

        return SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildStoreInvitation(),
                const SizedBox(height: 24),
                _buildProductPreview(),
                const SizedBox(height: 24),
                _buildReviewSection(),
                const SizedBox(height: 24),
                bloc.isStaticUser ? _buildSignInButton() : _buildSubmitButton(),
                const SizedBox(height: 16),
                _buildFooterText(),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildStoreInvitation() {
    return Text(
      '@${bloc.product.storehandle ?? 'storehandle'} has invited you to review a product you purchased outside Swadesic.',
      style: AppTextStyle.smallText(textColor: AppColors.appBlack),
    );
  }

  Widget _buildProductPreview() {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.appWhite,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Product Image with rounded corners
          Container(
            width: double.infinity,
            height: 200,
            decoration: BoxDecoration(
              color: AppColors.appWhite,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: bloc.product.prodImages != null &&
                    bloc.product.prodImages!.isNotEmpty
                ? SizedBox(
                    width: double.infinity,
                    height: 200,
                    child: Center(
                      child: PostAndProductImageWidgets(
                        localOrNetworkImage:
                            bloc.product.prodImages![0].productImage!,
                        imageSize: 167, // Scale down the image to 70%
                      ),
                    ),
                  )
                : Container(
                    width: double.infinity,
                    height: 200,
                    color: AppColors.lightGray,
                    child: Icon(
                      Icons.image,
                      color: AppColors.writingBlack1,
                      size: 48,
                    ),
                  ),
          ),
          // Product Details
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 5),
            // padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                // Store Icon using CustomImageContainer
                CustomImageContainer(
                  width: 45,
                  height: 45,
                  imageUrl: bloc.product.storeIcon != null &&
                          bloc.product.storeIcon!.isNotEmpty
                      ? bloc.product.storeIcon!
                      : null,
                  imageType: CustomImageContainerType.store,
                ),
                const SizedBox(width: 12),
                // Brand name + Product name with smart concatenation
                Expanded(
                  child: LayoutBuilder(
                    builder: (context, constraints) {
                      final productName = bloc.product.productName ?? 'Product';
                      final brandName = bloc.product.brandName;

                      // Create a TextPainter to measure the product name
                      final textPainter = TextPainter(
                        text: TextSpan(
                          text: productName,
                          style: AppTextStyle.contentText0(
                            textColor: AppColors.appBlack,
                          ),
                        ),
                        maxLines: 1,
                        textDirection: TextDirection.ltr,
                      )..layout(maxWidth: constraints.maxWidth);

                      final isProductNameSingleLine =
                          !textPainter.didExceedMaxLines;

                      if (brandName == null || brandName.isEmpty) {
                        return Text(
                          productName,
                          style: AppTextStyle.contentText0(
                            textColor: AppColors.appBlack,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        );
                      }

                      if (isProductNameSingleLine) {
                        // Brand on top, product name below
                        return Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              brandName,
                              style: AppTextStyle.contentHeading0(
                                textColor: AppColors.appBlack,
                              ).copyWith(fontWeight: FontWeight.w600),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                            Text(
                              productName,
                              style: AppTextStyle.contentText0(
                                textColor: AppColors.appBlack,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ],
                        );
                      } else {
                        // Concatenate brand and product name
                        return RichText(
                          text: TextSpan(
                            children: [
                              TextSpan(
                                text: '$brandName ',
                                style: AppTextStyle.contentHeading0(
                                  textColor: AppColors.appBlack,
                                ).copyWith(fontWeight: FontWeight.w600),
                              ),
                              TextSpan(
                                text: productName,
                                style: AppTextStyle.contentText0(
                                  textColor: AppColors.appBlack,
                                ),
                              ),
                            ],
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        );
                      }
                    },
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),
        ],
      ),
    );
  }

  Widget _buildReviewSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextField(
          controller: bloc.reviewController,
          decoration: InputDecoration(
            hintText: 'Write your review...',
            hintStyle: AppTextStyle.smallText(textColor: AppColors.appBlack),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: AppColors.lightGray),
            ),
            contentPadding: const EdgeInsets.all(16),
          ),
          style: AppTextStyle.smallText(textColor: AppColors.appBlack),
          maxLines: 5,
        ),
        const SizedBox(height: 16),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            RatingBar.builder(
              initialRating: bloc.rating,
              minRating: 1,
              direction: Axis.horizontal,
              allowHalfRating: false,
              itemCount: 5,
              itemSize: 32,
              itemPadding: const EdgeInsets.symmetric(horizontal: 4.0),
              itemBuilder: (context, _) => const Icon(
                Icons.star,
                color: Colors.amber,
              ),
              onRatingUpdate: bloc.onRatingChanged,
            ),
            TextButton(
              onPressed: bloc.onTapAddImages,
              child: Text(
                'add images',
                style: AppTextStyle.smallText(
                  textColor: AppColors.appBlack,
                  isUnderline: true,
                ),
              ),
            ),
          ],
        ),
        if (bloc.selectedImages.isNotEmpty) ...[
          const SizedBox(height: 16),
          SizedBox(
            height: 80,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: bloc.selectedImages.length,
              itemBuilder: (context, index) {
                return Container(
                  margin: const EdgeInsets.only(right: 8),
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    image: DecorationImage(
                      image: FileImage(bloc.selectedImages[index]),
                      fit: BoxFit.cover,
                    ),
                  ),
                  child: Stack(
                    alignment: Alignment.topRight,
                    children: [
                      GestureDetector(
                        onTap: () => bloc.onTapRemoveImage(index),
                        child: Container(
                          margin: const EdgeInsets.all(4),
                          padding: const EdgeInsets.all(2),
                          decoration: const BoxDecoration(
                            color: Colors.white,
                            shape: BoxShape.circle,
                          ),
                          child: Icon(
                            Icons.close,
                            size: 16,
                            color: AppColors.appBlack,
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildSubmitButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: bloc.onTapSubmitReview,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.brandBlack,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        child: Text(
          'Submit External Review',
          style: AppTextStyle.heading3Medium(
            textColor: AppColors.appWhite,
          ),
        ),
      ),
    );
  }

  Widget _buildSignInButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: bloc.onTapSignIn,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.brandBlack,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        child: Text(
          'Sign in to Add External Review',
          style: AppTextStyle.heading3Medium(
            textColor: AppColors.appWhite,
          ),
        ),
      ),
    );
  }

  Widget _buildFooterText() {
    return Text(
      'Your review helps sellers bring past feedback to Swadesic, making it easier for buyers like you to support Swadeshi businesses.',
      style: AppTextStyle.smallText(textColor: AppColors.appBlack),
      textAlign: TextAlign.center,
    );
  }
}
