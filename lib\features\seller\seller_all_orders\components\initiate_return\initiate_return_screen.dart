import 'package:expandable/expandable.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/seller/seller_all_orders/components/initiate_return/initiate_return_bloc.dart';
import 'package:swadesic/features/seller/seller_all_orders/seller_all_orders_common_widget.dart';
import 'package:swadesic/features/seller/seller_all_orders/seller_all_orders_bloc.dart';
import 'package:swadesic/model/order_response/get_order_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:swadesic/model/order_response/sub_order.dart';

class InitiateReturnScreen extends StatefulWidget {
  final List<SubOrder> suborderList;
  final SellerAllOrdersBloc sellerAllOrdersBloc;
  final Order order;
  const InitiateReturnScreen(
      {Key? key,
      required this.suborderList,
      required this.sellerAllOrdersBloc,
      required this.order})
      : super(key: key);

  @override
  State<InitiateReturnScreen> createState() => _InitiateReturnScreenState();
}

class _InitiateReturnScreenState extends State<InitiateReturnScreen> {
  // region Bloc
  late InitiateReturnBloc initiateReturnBloc;

  // endregion

  // region Init
  @override
  void initState() {
    initiateReturnBloc =
        InitiateReturnBloc(context, widget.sellerAllOrdersBloc, widget.order);
    initiateReturnBloc.init();
    super.initState();
  }

  // endregion

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          margin: const EdgeInsets.only(bottom: 10),
          color: AppColors.textFieldFill1,
          child: ExpandablePanel(
            theme: const ExpandableThemeData(
              animationDuration: Duration(microseconds: 100),
              iconPlacement: ExpandablePanelIconPlacement.right,
              // alignment: Alignment.bottomRight
              tapBodyToCollapse: true,
              tapHeaderToExpand: true,
              tapBodyToExpand: true,
              //useInkWell: false,
              iconPadding: EdgeInsets.symmetric(vertical: 10, horizontal: 10),
              //iconColor: Colors.green
            ),
            header: Container(
              padding: const EdgeInsets.all(10),
              color: AppColors.textFieldFill1,
              child: Row(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  SvgPicture.asset(AppImages.packageIcon),
                  horizontalSizedBox(10),
                  Expanded(
                    child: Text(
                      "Initiate return",
                      maxLines: 1,
                      style: TextStyle(
                        fontFamily: "LatoSemibold",
                        overflow: TextOverflow.visible,
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: AppColors.darkGray,
                      ),
                    ),
                  ),
                  Expanded(child: horizontalSizedBox(10)),
                ],
              ),
            ),
            collapsed: Container(
                color: AppColors.appWhite,
                child: Column(
                  children: [
                    // Your customer requested this return within the return window. Please initiate return process.
                    Container(
                      padding: const EdgeInsets.all(10),
                      width: double.infinity,
                      color: AppColors.appWhite,
                      child: Text(
                        "Your customer requested this return within the return window. Please initiate return process.",
                        style: TextStyle(
                          fontFamily: "LatoBold",
                          fontSize: 14,
                          fontWeight: FontWeight.w700,
                          color: AppColors.appBlack,
                        ),
                      ),
                    ),
                    tip("Know the return process"),
                    //Initiate all returns and Need Help?
                    confirmAndCancelAll(),
                  ],
                )),
            expanded: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Your customer requested this return within the return window. Please initiate return process.
                Container(
                  padding: const EdgeInsets.all(10),
                  width: double.infinity,
                  color: AppColors.appWhite,
                  child: Text(
                    "Your customer requested this return within the return window. Please initiate return process.",
                    style: TextStyle(
                      fontFamily: "LatoBold",
                      fontSize: 14,
                      fontWeight: FontWeight.w700,
                      color: AppColors.appBlack,
                    ),
                  ),
                ),
                tip("Know the return process"),
                //Initiate all returns and Need Help?
                confirmAndCancelAll(),
                Container(
                  height: 10,
                  color: AppColors.appWhite,
                ),
                // Padding(
                //   padding: const EdgeInsets.only(left: 20,right: 20,bottom: 30),
                //   child: divider(),
                // ),

                //Sub-order list
                Container(
                  color: AppColors.appWhite,
                  child: ListView.builder(
                      physics: const NeverScrollableScrollPhysics(),
                      padding: const EdgeInsets.symmetric(horizontal: 5),
                      shrinkWrap: true,
                      itemCount: widget.suborderList.length,
                      itemBuilder: (BuildContext, index) {
                        return Column(
                          children: [
                            //Divider
                            Container(
                              color: AppColors.appWhite,
                              child: Padding(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 20, vertical: 10),
                                child: divider(),
                              ),
                            ),
                            //Select and color change
                            InkWell(
                              onTap: () {
                                //widget.sellerAllOrdersBloc.onTapProduct(widget.suborderList[index].suborderNumber!);
                              },
                              child: Container(
                                decoration: BoxDecoration(
                                    color: widget.sellerAllOrdersBloc
                                            .selectedSubOrderNumberList
                                            .contains(widget.suborderList[index]
                                                .suborderNumber!)
                                        ? AppColors.tertiaryGreen
                                        : AppColors.appWhite,
                                    borderRadius: const BorderRadius.all(
                                        Radius.circular(10))),
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 10, vertical: 10),
                                child: Column(
                                  mainAxisSize: MainAxisSize.min,
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    productInfoCard(
                                        context: context,
                                        subOrder: widget.suborderList[index],
                                        isDeliveryReturnOnVisible: true,
                                        isReturnWindowLastDateVisible: true),
                                    //Reason
                                    Text(
                                      "Return reason: XXXXXXXXXXX",
                                      style: TextStyle(
                                          fontFamily: "LatoBold",
                                          fontWeight: FontWeight.w700,
                                          fontSize: 14,
                                          color: AppColors.darkGray),
                                    ),
                                    verticalSizedBox(10),
                                    //Initiate this return
                                    Row(
                                      mainAxisSize: MainAxisSize.max,
                                      mainAxisAlignment:
                                          MainAxisAlignment.start,
                                      crossAxisAlignment:
                                          CrossAxisAlignment.center,
                                      children: [
                                        InkWell(
                                          onTap: () {},
                                          child: Container(
                                            padding: const EdgeInsets.symmetric(
                                                vertical: 10, horizontal: 10),
                                            decoration: BoxDecoration(
                                              color: AppColors.brandBlack,
                                              borderRadius: BorderRadius.all(
                                                  Radius.circular(10)),
                                            ),
                                            child: Center(
                                              child: Text(
                                                "Initiate this return separately",
                                                style: TextStyle(
                                                    fontFamily: "LatoBold",
                                                    fontWeight: FontWeight.w700,
                                                    fontSize: 14,
                                                    color: AppColors.appWhite),
                                              ),
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        );
                      }),
                ),
              ],
            ),
          ),
        ),
        //endregion
      ],
    );
  }

  //region Confirm all suborder and need help
  Widget confirmAndCancelAll() {
    return Container(
      color: AppColors.appWhite,
      child: Padding(
        padding: const EdgeInsets.only(top: 10, left: 10, right: 10),
        child: Row(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            sellerAllOrderActionButton(
                buttonName: AppStrings.initiateReturn,
                onPress: () {
                  initiateReturnBloc.onTapInitiateReturn(widget.suborderList);
                }),
            horizontalSizedBox(10),
            sellerAllOrderCancelButton(buttonName: AppStrings.needHelp),
          ],
        ),
      ),
    );
  }
//endregion
}
