import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_widgets.dart';

class PickupPinCode extends StatefulWidget {
  final dynamic onTapDone;
  const PickupPinCode({Key? key, required this.onTapDone}) : super(key: key);

  @override
  State<PickupPinCode> createState() => _PickupPinCodeState();
}

class _PickupPinCodeState extends State<PickupPinCode> {
  //region Text ctrl
  final TextEditingController textCtrl = TextEditingController();
  //endregion
  @override
  Widget build(BuildContext context) {
    return body();
  }


  //region body
Widget body(){
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          TextFormField(
            controller: textCtrl,
            inputFormatters: <TextInputFormatter>[
              FilteringTextInputFormatter.allow(RegExp(r'[0-9]')),
              FilteringTextInputFormatter.digitsOnly,
              LengthLimitingTextInputFormatter(6)
            ],
            keyboardType: TextInputType.number,
            onChanged: (value){
              setState(() {

              });
            },

            // controller: widget.buyerHomeBloc.postalCodeTextCtrl,

            maxLines: 1,
            // readOnly: true,

            style: TextStyle(fontSize: 14, fontWeight: FontWeight.w400, color: AppColors.appBlack),
            decoration: InputDecoration(
              // prefixIcon: Padding(
              //   padding: EdgeInsets.symmetric(horizontal: 11.73.h),
              //   child: SvgPicture.asset(AppImages.searchBarIcon,fit: BoxFit.contain,color: AppColors.appBlack7,),
              // ),
              suffixIcon: Visibility(
                visible: textCtrl.text.isNotEmpty,
                child: CupertinoButton(
                    padding: EdgeInsets.zero,
                    onPressed: () {
                      // widget.buyerHomeBloc.sendPinCode();
                    },
                    child: Icon(
                      Icons.done,
                      color: AppColors.darkGray,
                      size: 25,
                    )),
              ),
              filled: true,

              //contentPadding: EdgeInsets.symmetric(vertical: 19.53.h),

              fillColor: AppColors.textFieldFill1,

              isDense: true,

              hintText: AppStrings.enterPickupPinCode,
              hintStyle: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w400,
                  color: AppColors.writingColor3
              ),
              focusedBorder: OutlineInputBorder(borderRadius: BorderRadius.circular(22), borderSide: BorderSide.none),
              enabledBorder: OutlineInputBorder(borderRadius: BorderRadius.circular(22), borderSide: BorderSide.none),
            ),
          ),
          verticalSizedBox(10),
        ],
      ),
    );
}
//endregion


}
