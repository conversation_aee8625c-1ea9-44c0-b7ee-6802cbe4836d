import 'package:expandable/expandable.dart';
import 'package:flutter/material.dart';
import 'package:swadesic/features/seller/seller_all_orders/components/confirmed_not_yet_shipped/confirmed_not_yet_shipped_bloc.dart';
import 'package:swadesic/features/seller/seller_all_orders/seller_all_orders_common_widget.dart';
import 'package:swadesic/features/seller/seller_all_orders/seller_sub_order/seller_sub_order_bloc.dart';
import 'package:swadesic/model/order_response/get_order_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:swadesic/model/order_response/sub_order.dart';

class ConfirmedNotYetShippedScreen extends StatefulWidget {
  final List<SubOrder> suborderList;
  final SellerSubOrderBloc sellerSubOrderBloc;
  final Order order;

  const ConfirmedNotYetShippedScreen(
      {Key? key,
      required this.suborderList,
      required this.sellerSubOrderBloc,
      required this.order})
      : super(key: key);

  @override
  State<ConfirmedNotYetShippedScreen> createState() =>
      _ConfirmedNotYetShippedScreenState();
}

class _ConfirmedNotYetShippedScreenState
    extends State<ConfirmedNotYetShippedScreen> {
  // region Bloc
  late ConfirmedNotYetShippedBloc confirmedNotYetShippedBloc;

  // endregion

  // region Init
  @override
  void initState() {
    confirmedNotYetShippedBloc = ConfirmedNotYetShippedBloc(
        context, widget.sellerSubOrderBloc, widget.suborderList, widget.order);
    confirmedNotYetShippedBloc.init();
    super.initState();
  }

  // endregion

  //region Dis update
  @override
  void didUpdateWidget(covariant ConfirmedNotYetShippedScreen oldWidget) {
    confirmedNotYetShippedBloc = ConfirmedNotYetShippedBloc(
        context, widget.sellerSubOrderBloc, widget.suborderList, widget.order);
    confirmedNotYetShippedBloc.init();
    super.didUpdateWidget(oldWidget);
  }

  //endregion

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 30, horizontal: 10),
      decoration: BoxDecoration(
          color: AppColors.appWhite,
          border: Border(bottom: BorderSide(color: AppColors.lightStroke))),
      child: ExpandablePanel(
        //region Theme
        theme: ExpandableThemeData(
          animationDuration: Duration(microseconds: 100),
          iconPlacement: ExpandablePanelIconPlacement.right,
          // alignment: Alignment.bottomRight
          tapBodyToCollapse: true,
          tapHeaderToExpand: true,
          tapBodyToExpand: true,
          iconPadding: EdgeInsets.zero,
          //useInkWell: false,
          // iconRotationAngle:math.pi/2,
          headerAlignment: ExpandablePanelHeaderAlignment.center,

          iconSize: 40,
          iconColor: AppColors.appBlack,

          // iconPadding: EdgeInsets.symmetric( horizontal: 10),
          //iconColor: Colors.green
        ),
        //endregion

        //Waiting for confirmation
        //region Header
        header: header(),
        //endregion
        collapsed: confirmAndCancelAll(),

        expanded: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            confirmAndCancelAll(),
            Container(
              padding: const EdgeInsets.only(top: 30),
              color: AppColors.appWhite,
              child: ListView.builder(
                  physics: const NeverScrollableScrollPhysics(),
                  shrinkWrap: true,
                  itemCount: confirmedNotYetShippedBloc.suborderList.length,
                  itemBuilder: (context, index) {
                    return Column(
                      children: [
                        Column(
                          mainAxisSize: MainAxisSize.min,
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            verticalSizedBox(10),
                            //Delivery estimation
                            Text(
                              "${AppStrings.deliveryEstimate} ${confirmedNotYetShippedBloc.suborderList[index].estimatedDeliveryDate!.replaceAll("/", "-")}",
                              style: AppTextStyle.contentHeading0(
                                  textColor: AppColors.appBlack),
                            ),
                            productInfoCard(
                              context: context,
                              subOrder: confirmedNotYetShippedBloc
                                  .suborderList[index],
                            ),

                            ///Sub order Buttons
                            Row(
                              mainAxisSize: MainAxisSize.max,
                              mainAxisAlignment: MainAxisAlignment.start,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                AppCommonWidgets.subOrderButton(
                                    buttonName:
                                        AppStrings.startShippingSeparately,
                                    onTap: () {
                                      //Mark only selected
                                      confirmedNotYetShippedBloc
                                          .suborderList[index]
                                          .isSelected = true;
                                      //Open bottom sheet
                                      confirmedNotYetShippedBloc
                                          .startShippingAndCancelAll();
                                    },
                                    horizontalPadding: 10),
                                horizontalSizedBox(10),
                                AppCommonWidgets.subOrderButton(
                                    buttonName: AppStrings.cancelThisAlone,
                                    onTap: () {
                                      //Mark only selected
                                      confirmedNotYetShippedBloc
                                          .suborderList[index]
                                          .isSelected = true;
                                      //Open bottom sheet
                                      confirmedNotYetShippedBloc
                                          .startShippingAndCancelAll();
                                    },
                                    horizontalPadding: 10),
                              ],
                            ),
                            verticalSizedBox(10)
                          ],
                        ),
                        //Divider
                        //Divider
                        Visibility(
                          visible:
                              confirmedNotYetShippedBloc.suborderList.length -
                                      1 !=
                                  index,
                          child: Container(
                            color: AppColors.appWhite,
                            child: Padding(
                              padding: const EdgeInsets.symmetric(vertical: 10),
                              child: divider(),
                            ),
                          ),
                        ),
                      ],
                    );
                  }),
            ),
          ],
        ),
      ),
    );
  }

  //region Header
  Widget header() {
    return SellerAllOrdersCommonWidgets.sellerCommonComponent(
      icon: AppImages.thumbUpIconPng,
      componentName: AppStrings.confirmedNotYetShipped,
      suborderList: widget.suborderList,
      additionalWidgets: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            alignment: Alignment.centerLeft,
            child: Text(
              AppStrings.customersLove,
              style:
                  AppTextStyle.contentHeading0(textColor: AppColors.brandBlack),
            ),
          ),
          verticalSizedBox(5),
          Container(
            alignment: Alignment.centerLeft,
            child: Text(
              AppStrings.youStartedShipping,
              style:
                  AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
            ),
          ),
        ],
      ),
    );
  }

  //endregion

  //region Confirm and cancel all
  Widget confirmAndCancelAll() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Expanded(
            child: AppCommonWidgets.activeButton(
                buttonName: AppStrings.startShippingAll,
                onTap: () {
                  CommonMethods.subOrderSelectUnSelectAll(
                      isSelectAll: true,
                      subOrderList: confirmedNotYetShippedBloc.suborderList);
                  confirmedNotYetShippedBloc.startShippingAndCancelAll();
                  // CommonMethods.subOrderSelectUnSelectAll(isSelectAll: true, subOrderList: waitingForConfirmationBloc.suborderList);
                  // waitingForConfirmationBloc.onTrapConfirmAndCancelAll();
                })),
        horizontalSizedBox(10),
        Expanded(
            child: AppCommonWidgets.inActiveButton(
                buttonName: AppStrings.cancelAll,
                onTap: () {
                  CommonMethods.subOrderSelectUnSelectAll(
                      isSelectAll: true,
                      subOrderList: confirmedNotYetShippedBloc.suborderList);
                  confirmedNotYetShippedBloc.startShippingAndCancelAll();
                })),
      ],
    );
  }

//endregion
}
