import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/buyer_view_product/buyer_view_product_list/buyer_view_product_screen.dart';
import 'package:swadesic/features/common_buyer_seller_screen/product_detail_full_card/product_detail_full_card.dart';
import 'package:swadesic/features/seller/add_edit_product_fields/add_edit_product_fields_bloc.dart';
import 'package:swadesic/features/seller/product_preview/product_preview_bloc.dart';
import 'package:swadesic/model/seller_delivery_setting_response/seller_delivery_setting_response.dart';
import 'package:swadesic/model/seller_return_warranty_response/seller_return_warranty_response.dart';
import 'package:swadesic/model/store_product_response/store_product_response.dart';
import 'package:swadesic/model/product_variant/product_variant.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_enums.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_widgets.dart';

// region AddProductPreviewScreen
class ProductPreviewScreen extends StatefulWidget {
  final int storeId;
  final String storeReference;
  final SellerDeliveryStoreResponse? sellerDeliveryStoreResponse;
  final SellerReturnWarrantyResponse? returnSetting;
  final Product product;

  const ProductPreviewScreen(
      {Key? key,
      required this.storeReference,
      required this.storeId,
      this.sellerDeliveryStoreResponse,
      this.returnSetting,
      required this.product})
      : super(key: key);

  @override
  _ProductPreviewScreenState createState() => _ProductPreviewScreenState();
}
// endregion

class _ProductPreviewScreenState extends State<ProductPreviewScreen> {
  // region Bloc
  late ProductPreviewBloc productPreviewBloc;

  // endregion

  // region Init
  @override
  void initState() {
    if (widget.sellerDeliveryStoreResponse!.message == null) {
      //print("Delivery is null");
    } else {
      //print("We have data ");
    }
    productPreviewBloc = ProductPreviewBloc(
        context,
        widget.storeId,
        widget.storeReference,
        widget.sellerDeliveryStoreResponse ?? SellerDeliveryStoreResponse(),
        widget.returnSetting,
        widget.product);
    productPreviewBloc.init();
    super.initState();
  }

  // endregion

  // region build
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.appWhite,
      appBar: appBar(),
      body: SafeArea(
          child: StreamBuilder<ProductPreviewState>(
              stream: productPreviewBloc.publishCtrl.stream,
              // initialData: ProductPreviewState.Loading,
              builder: (context, snapshot) {
                if (snapshot.data == ProductPreviewState.Loading) {
                  return Center(child: AppCommonWidgets.appCircularProgress());
                }
                return body();
              })),
    );
  }

  // endregion

  //region Appbar
  AppBar appBar() {
    return AppCommonWidgets.mainAppBar(
        context: context,
        isCustomTitle: false,
        title: AppStrings.productPreview,
        isDefaultMenuVisible: false,
        isMembershipVisible: false,
        isCartVisible: false,
        isTextButtonVisible: true,
        textButtonWidget: StreamBuilder<ProductPreviewState>(
            stream: productPreviewBloc.publishCtrl.stream,
            builder: (context, snapshot) {
              if (snapshot.data == ProductPreviewState.Loading) {
                return const SizedBox();
              }
              return AppCommonWidgets.appBarTextButtonText(
                  text: AppStrings.publish);
            }),
        onTapTextButton: () {
          productPreviewBloc.addProductDetailApiCall();
          // productPreviewBloc.addReturnAndWarranty();
        });
  }

  //endregion

  //region Body
  Widget body() {
    // return BuyerViewProductScreen(productList: [widget.product], index: 0,
    //       isFromAddProduct: true,
    //   openingFrom: SearchScreenEnum.NON,
    //
    //
    // );
    return SingleChildScrollView(
      child: ProductDetailFullCard(
        product: widget.product,
        isFromAddProduct: true,
        isGoToLatestVersion: false,
      ),
    );
  }
  //endregion

  // region Body
  Widget body1() {
    return SingleChildScrollView(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.center,
        // padding: EdgeInsets.zero,
        children: [
          Container(
              color: AppColors.textFieldFill1,
              height: MediaQuery.of(context).size.width,
              width: MediaQuery.of(context).size.width,
              child: productImage()),
          verticalSizedBox(1),

          ///
          brandProduct(),
          // ///
          sellingMrp(),
          // Variant selection if variants exist
          variantSelection(),
          // // verticalSizedBox(10),
          storeHandle(),
          //
          verticalSizedBox(10),
          productDetails(),
          verticalSizedBox(10),
          Padding(
            padding: const EdgeInsets.only(left: 16, right: 9),
            child: divider(),
          ),
          verticalSizedBox(11),
          buyDetail(),
          verticalSizedBox(11),
          Padding(
            padding: const EdgeInsets.only(left: 16, right: 9),
            child: divider(),
          ),
          verticalSizedBox(10),
          addComment(),
          //
          verticalSizedBox(10),
          viewComment(),
          //
          verticalSizedBox(10),
        ],
      ),
    );
  }

  // endregion

  //region Product images
  Widget productImage() {
    int onScreenImageIndex = 0;
    return Stack(
      alignment: Alignment.center,
      children: [
        PageView.builder(
            allowImplicitScrolling: true,
            onPageChanged: (index) {
              onScreenImageIndex = index;
              //print(index);
            },
            itemCount: AppConstants.multipleSelectedImage.length,

            ///controller: buyerViewProductBloc.imageSliderPageCtrl,
            itemBuilder: (context, index) {
              return Image.file(
                File(
                  AppConstants.multipleSelectedImage[index].path,
                ),
                height: MediaQuery.of(context).size.width,
                width: MediaQuery.of(context).size.width,
                fit: BoxFit.cover,
              );
            }),
      ],
    );
  }
//endregion

//region Brand name And Product name
  Widget brandProduct() {
    return Padding(
      padding: const EdgeInsets.only(left: 16, right: 16, top: 10),
      child: Align(
        alignment: Alignment.centerLeft,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              alignment: Alignment.centerLeft,
              height: 25,
              child: Text(AddEditProductFieldsBloc.brandNameTextCtrl.text,
                  style:
                      AppTextStyle.heading4Bold(textColor: AppColors.appBlack)),
            ),
            Text(AddEditProductFieldsBloc.productNameTextCtrl.text,
                maxLines: 3,
                style: AppTextStyle.heading3Medium(
                    textColor: AppColors.writingColor2)),
          ],
        ),
      ),
    );
  }
//endregion

//region Selling Price and MRP
  Widget sellingMrp() {
    return Padding(
      padding: const EdgeInsets.only(left: 16, right: 16, top: 7, bottom: 7),
      child: Align(
        alignment: Alignment.centerLeft,
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text("₹ ${productPreviewBloc.getDisplaySellingPrice()}",
                style: AppTextStyle.sectionSemiBold(
                    textColor: AppColors.appBlack)),
            horizontalSizedBox(17),
            Text("₹ ${productPreviewBloc.getDisplayMrpPrice()}",
                style: AppTextStyle.sectionSemiBold(
                    textColor: AppColors.writingColor3)),
          ],
        ),
      ),
    );
  }

//endregion

//region Variant Selection
  Widget variantSelection() {
    // Only show variant selection if product has options and variants
    if (widget.product.options == null ||
        widget.product.options!.isEmpty ||
        widget.product.variants == null ||
        widget.product.variants!.isEmpty) {
      return const SizedBox.shrink();
    }

    final variants = widget.product.variants!
        .map((variantJson) {
          try {
            return ProductVariant.fromJson(variantJson);
          } catch (e) {
            return null;
          }
        })
        .where((variant) => variant != null && variant.combinations.isNotEmpty) // Only show variants with combinations
        .cast<ProductVariant>()
        .toList();

    if (variants.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "Available Options:",
            style: AppTextStyle.contentText0(textColor: AppColors.appBlack)
                .copyWith(fontWeight: FontWeight.w600),
          ),
          verticalSizedBox(10),
          // Show options for the selected variant
          if (productPreviewBloc.selectedVariant != null) ...[
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: productPreviewBloc.selectedVariant!.combinations.entries.map((entry) {
                return Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: AppColors.textFieldFill1,
                    borderRadius: BorderRadius.circular(15),
                    border: Border.all(color: AppColors.appBlack.withOpacity(0.2)),
                  ),
                  child: Text(
                    "${entry.key}: ${entry.value}",
                    style: AppTextStyle.access0(textColor: AppColors.appBlack),
                  ),
                );
              }).toList(),
            ),
            verticalSizedBox(8),
            Text(
              "Stock: ${productPreviewBloc.getDisplayStock()}",
              style: AppTextStyle.access0(textColor: AppColors.appBlack),
            ),
          ],
        ],
      ),
    );
  }
//endregion

//region Store Handle
  Widget storeHandle() {
    return Container(
      alignment: Alignment.centerLeft,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      height: 27,
      child: CupertinoButton(
        onPressed: () {},
        padding: EdgeInsets.zero,
        child: Row(
          children: [
            Text(
              "store:",
              style: AppTextStyle.heading3Medium(
                  textColor: AppColors.writingColor2),
            ),
            horizontalSizedBox(5),
            InkWell(
              child: Text(
                AppStrings.storeHandle.toLowerCase(),
                style: AppTextStyle.heading3Medium(
                    textColor: AppColors.brandBlack),
              ),
            )
          ],
        ),
      ),
    );
  }

//endregion
//
//region Product Details
  Widget productDetails() {
    return InkWell(
      onTap: () {
        productPreviewBloc.buyerProductDetail();
      },
      child: Padding(
        padding: const EdgeInsets.only(left: 16, right: 9),
        child: SizedBox(
          height: 29,
          child: Row(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Text(AppStrings.productDetails.toLowerCase(),
                  style:
                      AppTextStyle.heading1Bold(textColor: AppColors.appBlack)),
              SvgPicture.asset(
                AppImages.productDetailsRight,
                color: AppColors.writingColor2,
                fit: BoxFit.fill,
              )
            ],
          ),
        ),
      ),
    );
  }

//endregion
//
//region Buy Now and View detail
  Widget buyDetail() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 18),
      child: Row(
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          //
          Expanded(
            child: Container(
              decoration: const BoxDecoration(
                  borderRadius: BorderRadius.all(Radius.circular(10))),
              child: CupertinoButton(
                borderRadius: BorderRadius.circular(150),
                color: AppColors.brandBlack,
                padding: EdgeInsets.zero,
                child: Center(
                  child: Text(
                    "Buy now",
                    style:
                        AppTextStyle.button2Bold(textColor: AppColors.appWhite),
                  ),
                ),
                onPressed: () {},
              ),
            ),
          ),
          horizontalSizedBox(10),
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                  borderRadius: const BorderRadius.all(Radius.circular(150)),
                  border: Border.all(color: AppColors.darkGray)),
              child: CupertinoButton(
                  borderRadius: BorderRadius.circular(150),
                  color: AppColors.appWhite,
                  padding: EdgeInsets.zero,
                  child: Center(
                    child: Text(
                      AppStrings.viewDetails,
                      style: AppTextStyle.button2Bold(
                          textColor: AppColors.writingColor2),
                    ),
                  ),
                  onPressed: () {
                    productPreviewBloc.buyerProductDetail();
                  }),
            ),
          ),
          horizontalSizedBox(50),

          CupertinoButton(
              padding: EdgeInsets.zero,
              onPressed: () {},
              child: SvgPicture.asset(
                AppImages.shareIcon,
                color: AppColors.brandBlack,
              ))
        ],
      ),
    );
  }
//endregion

//region View All Comment
  Widget viewComment() {
    return InkWell(
      onTap: () {},
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: Row(
          children: [
            Text(
              "View all comments",
              textAlign: TextAlign.left,
              style: TextStyle(
                fontWeight: FontWeight.w600,
                fontFamily: "LatoSemibold",
                fontSize: 15,
                color: AppColors.writingColor3,
              ),
            ),
            Expanded(child: horizontalSizedBox(10)),
          ],
        ),
      ),
    );
  }

//endregion
//
  //region Add Comment
  Widget addComment() {
    return InkWell(
      onTap: () {},
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 10),
        height: 43,
        decoration: BoxDecoration(
          color: AppColors.textFieldFill1,
        ),
        child: Row(
          children: [
            SvgPicture.asset(
              AppImages.emoji,
              fit: BoxFit.contain,
            ),
            Expanded(
              child: TextFormField(
                enabled: false,

                readOnly: true,

                maxLines: 1,
                //controller: addProductBloc.hashTagsTextCtrl,

                style: AppTextStyle.heading4Regular(
                    textColor: AppColors.writingColor3),
                decoration: InputDecoration(
                  // prefixIcon: SvgPicture.asset(AppImages.emoji,fit:BoxFit.contain,),
                  filled: true,
                  contentPadding:
                      const EdgeInsets.symmetric(vertical: 10, horizontal: 10),
                  fillColor: AppColors.textFieldFill1,
                  isDense: true,
                  hintText: AppStrings.commentHint,
                  hintStyle: AppTextStyle.heading4Regular(
                      textColor: AppColors.writingColor3),
                  border: InputBorder.none,
                  // focusedBorder: OutlineInputBorder(
                  //     borderRadius: BorderRadius.circular(22),
                  //     borderSide: BorderSide.none
                  //
                  // ),
                  // enabledBorder: OutlineInputBorder(
                  //
                  //     borderRadius: BorderRadius.circular(22),
                  //     borderSide: BorderSide.none
                  // ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
  //endregion
}
