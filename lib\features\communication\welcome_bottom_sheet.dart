import 'package:flutter/material.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/features/buyers/buyer_home/home_access/home_access_bloc.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/app_colors.dart';

class WelcomeBottomSheet extends StatelessWidget {
  final String inviteCode;
  final HomeAccessBloc homeAccessBloc;

  const WelcomeBottomSheet({
    Key? key,
    required this.inviteCode,
    required this.homeAccessBloc,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      constraints: BoxConstraints(
        maxHeight:
            MediaQuery.of(context).size.height * 0.85, // 85% of screen height
      ),
      child: SingleChildScrollView(
        child: Stack(
          children: [
            Container(
              padding: EdgeInsets.only(
                left: 24,
                right: 24,
                top: 80,
                bottom: MediaQuery.of(context).viewInsets.bottom + 24,
              ),
              decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(20),
                  topRight: Radius.circular(20),
                ),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Welcome to Swadesic Stores',
                    style:
                        AppTextStyle.exHeading1(textColor: AppColors.appBlack),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    "Imagine a space where every customer isn't just a buyer—they're part of a loyal community.\n"
                    "They, \n \t\t\t - Admire your brand story \n \t\t\t - Interact with your products \n \t\t\t - Share genuine feedback, and \n \t\t\t - Naturally promote your store.\n"
                    "They actively post about your products in the marketplace, driving increased and repeat sales.",
                    style: AppTextStyle.contentHeading0(
                        textColor: AppColors.appBlack),
                  ),
                  const SizedBox(height: 24),
                  _buildFeatureItem(
                    Icons.local_fire_department,
                    'Built for Swadeshi Entrepreneurs',
                    'Sell, scale, and build powerful brands with loyal customer community.',
                  ),
                  const SizedBox(height: 16),
                  _buildFeatureItem(
                    Icons.groups,
                    'Experience E-commerce 2.0 & Elevate Your Customer Experience',
                    'Drive genuine customer engagement that drives growth. Make selling meets storytelling and',
                  ),
                  const SizedBox(height: 16),
                  _buildFeatureItem(
                    Icons.trending_up,
                    'Unlimited & Risk-Free Growth',
                    'List unlimited products and share your unique content, sell risk-free with zero commissions on orders under ₹500 and free returns, cancels.',
                  ),
                  const SizedBox(height: 24),
                  InkWell(
                    onTap: () {
                      CommonMethods.openAppWebView(
                          webUrl: 'https://swadesic.sociallyx.com/business',
                          context: context);
                    },
                    child: Text(
                      'Learn more about Swadesic Stores',
                      style:
                          AppTextStyle.access0(textColor: AppColors.brandBlack),
                    ),
                  ),
                  const SizedBox(height: 24),
                  Row(
                    children: [
                      Expanded(
                        child: OutlinedButton(
                          onPressed: () {
                            CommonMethods.share(
                                "${AppConstants.appData.isUserView! ? AppStrings.userInviteeMessage : AppStrings.storeInviteeMessage}${AppConstants.domainName}?ref=$inviteCode");
                          },
                          style: OutlinedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            side: BorderSide(color: AppColors.appBlack),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                          child: Text(
                            'Invite a Friend',
                            style: AppTextStyle.access0(
                                textColor: AppColors.appBlack),
                          ),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: ElevatedButton(
                          onPressed: () => homeAccessBloc.onTapCreateStore(),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColors.appBlack,
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                          child: Text(
                            'Continue',
                            style: AppTextStyle.access0(
                                textColor: AppColors.appWhite),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            Positioned(
              top: 16,
              right: 16,
              child: IconButton(
                icon: Icon(Icons.close),
                onPressed: () {
                  Navigator.of(context).pop();
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeatureItem(IconData icon, String title, String description) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(icon, size: 28, color: AppColors.appBlack),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: AppTextStyle.access1(textColor: AppColors.appBlack),
              ),
              const SizedBox(height: 4),
              Text(
                description,
                style: AppTextStyle.contentText0(
                    textColor: AppColors.writingBlack),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
