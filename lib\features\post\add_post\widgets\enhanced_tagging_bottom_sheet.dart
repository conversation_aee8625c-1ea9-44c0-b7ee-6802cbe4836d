import 'package:flutter/material.dart';
import 'package:swadesic/features/post/add_post/widgets/enhanced_typing_suggestions_overlay.dart';
import 'package:swadesic/features/widgets/custom_image_container/custom_image_container.dart';
import 'package:swadesic/model/typing_suggestions_response/typing_suggestions_response.dart';
import 'package:swadesic/services/typing_suggestions_service/typing_suggestions_service.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

enum BottomSheetState {
  suggestions,
  storeOptions,
  storeProducts,
}

class EnhancedTaggingBottomSheet extends StatefulWidget {
  final List<SuggestionItem> initialSuggestions;
  final Function(SuggestionItem) onItemSelected;
  final bool isLoading;
  final VoidCallback? onLoadMore;
  final bool hasMore;
  final Function(String)? onSearchChanged;

  const EnhancedTaggingBottomSheet({
    super.key,
    required this.initialSuggestions,
    required this.onItemSelected,
    this.isLoading = false,
    this.onLoadMore,
    this.hasMore = false,
    this.onSearchChanged,
  });

  @override
  State<EnhancedTaggingBottomSheet> createState() =>
      _EnhancedTaggingBottomSheetState();
}

class _EnhancedTaggingBottomSheetState
    extends State<EnhancedTaggingBottomSheet> {
  BottomSheetState currentState = BottomSheetState.suggestions;
  SuggestionItem? selectedStore;
  List<SuggestionItem> storeProducts = [];
  bool isLoadingProducts = false;
  final TypingSuggestionsService _service = TypingSuggestionsService();

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.8,
      decoration: BoxDecoration(
        color: AppColors.appWhite,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Column(
        children: [
          _buildHeader(),
          Expanded(
            child: _buildContent(),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    String title;
    bool showBackButton = false;

    switch (currentState) {
      case BottomSheetState.suggestions:
        title = 'Tag Store, Product or Member';
        break;
      case BottomSheetState.storeOptions:
        title = 'Tag Store';
        showBackButton = true;
        break;
      case BottomSheetState.storeProducts:
        title = 'Select Product';
        showBackButton = true;
        break;
    }

    return Container(
      padding: const EdgeInsets.only(left: 16, right: 16, top: 10),
      child: Row(
        children: [
          if (showBackButton)
            IconButton(
              onPressed: _goBack,
              icon: const Icon(Icons.arrow_back),
            ),
          Expanded(
            child: Text(
              title,
              style:
                  AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
            ),
          ),
          IconButton(
            onPressed: () => Navigator.pop(context),
            icon: const Icon(Icons.close),
          ),
        ],
      ),
    );
  }

  Widget _buildContent() {
    switch (currentState) {
      case BottomSheetState.suggestions:
        return _buildSuggestionsList();
      case BottomSheetState.storeOptions:
        return _buildStoreOptions();
      case BottomSheetState.storeProducts:
        return _buildStoreProductsList();
    }
  }

  Widget _buildSuggestionsList() {
    return Container(
      child: EnhancedTypingSuggestionsOverlay(
        suggestions: widget.initialSuggestions,
        onSuggestionTap: _handleSuggestionTap,
        isLoading: widget.isLoading,
        onLoadMore: widget.onLoadMore,
        hasMore: widget.hasMore,
        showSearchBar: true,
        onSearchChanged: widget.onSearchChanged,
      ),
    );
  }

  Widget _buildStoreOptions() {
    if (selectedStore == null) return const SizedBox.shrink();

    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // Store info
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppColors.textFieldFill1,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                CustomImageContainer(
                  width: 42,
                  height: 42,
                  imageUrl: selectedStore!.imageUrl,
                  imageType: CustomImageContainerType.store,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        selectedStore!.primaryText ?? '',
                        style: AppTextStyle.contentHeading0(
                          textColor: AppColors.appBlack,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      if (selectedStore!.secondaryText != null)
                        Text(
                          selectedStore!.secondaryText!,
                          style: AppTextStyle.contentText0(
                            textColor: AppColors.writingBlack1,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 24),

          // Options
          _buildStoreOption(
            'Tag Store Only',
            'Mention the store directly',
            Icons.store,
            () {
              widget.onItemSelected(selectedStore!);
              Navigator.pop(context);
            },
          ),
          const SizedBox(height: 12),
          _buildStoreOption(
            'Tag Store Products',
            'Choose a specific product from this store',
            Icons.inventory,
            () {
              _showStoreProducts();
            },
          ),
        ],
      ),
    );
  }

  Widget _buildStoreOption(
    String title,
    String subtitle,
    IconData icon,
    VoidCallback onTap,
  ) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          border: Border.all(color: AppColors.textFieldFill0),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            Icon(
              icon,
              color: AppColors.brandBlack,
              size: 24,
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: AppTextStyle.contentHeading0(
                      textColor: AppColors.appBlack,
                    ),
                  ),
                  Text(
                    subtitle,
                    style: AppTextStyle.contentText0(
                      textColor: AppColors.writingBlack1,
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              color: AppColors.writingBlack1,
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStoreProductsList() {
    if (isLoadingProducts && storeProducts.isEmpty) {
      return AppCommonWidgets.appCircularProgress();
    }

    if (storeProducts.isEmpty) {
      return Center(
        child: Text(
          'No products found',
          style: AppTextStyle.contentText0(
            textColor: AppColors.writingBlack1,
          ),
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: storeProducts.length,
      itemBuilder: (context, index) {
        final product = storeProducts[index];
        return InkWell(
          onTap: () {
            widget.onItemSelected(product);
            Navigator.pop(context);
          },
          child: Container(
            padding: const EdgeInsets.symmetric(vertical: 12),
            child: Row(
              children: [
                CustomImageContainer(
                  width: 40,
                  height: 40,
                  imageUrl: product.imageUrl,
                  imageType: CustomImageContainerType.product,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        product.primaryText ?? '',
                        style: AppTextStyle.contentHeading0(
                          textColor: AppColors.appBlack,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      if (product.secondaryText != null)
                        Text(
                          product.secondaryText!,
                          style: AppTextStyle.contentText0(
                            textColor: AppColors.writingBlack1,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  void _handleSuggestionTap(SuggestionItem suggestion) {
    if (suggestion.type == 'STORE') {
      setState(() {
        selectedStore = suggestion;
        currentState = BottomSheetState.storeOptions;
      });
    } else {
      widget.onItemSelected(suggestion);
      Navigator.pop(context);
    }
  }

  void _showStoreProducts() async {
    if (selectedStore == null) return;

    setState(() {
      currentState = BottomSheetState.storeProducts;
      isLoadingProducts = true;
      storeProducts.clear();
    });

    try {
      String query = "@${selectedStore!.primaryText}/";
      String visitorReference = AppConstants.appData.isUserView!
          ? AppConstants.appData.userReference!
          : AppConstants.appData.storeReference!;

      final response = await _service.getTypingSuggestions(
        query: query,
        limit: 20,
        offset: 0,
        visitorReference: visitorReference,
        userPincode: AppConstants.appData.pinCode,
      );

      setState(() {
        storeProducts = (response.results ?? []).map((product) {
          // Ensure each product has the correct type set
          return SuggestionItem(
            type: 'PRODUCT',
            reference: product.reference,
            primaryText: product.primaryText,
            secondaryText: product.secondaryText,
            imageUrl: product.imageUrl,
            storeHandle: product.storeHandle,
            storeName: product.storeName,
          );
        }).toList();
        isLoadingProducts = false;
      });
    } catch (e) {
      setState(() {
        isLoadingProducts = false;
      });
      if (mounted) {
        CommonMethods.toastMessage('Failed to load products', context);
      }
    }
  }

  void _goBack() {
    setState(() {
      if (currentState == BottomSheetState.storeOptions) {
        currentState = BottomSheetState.suggestions;
        selectedStore = null;
      } else if (currentState == BottomSheetState.storeProducts) {
        currentState = BottomSheetState.storeOptions;
        storeProducts.clear();
      }
    });
  }
}
