import 'package:expandable/expandable.dart';
import 'package:flutter/material.dart';
import 'package:swadesic/features/seller/seller_all_orders/components/customer_cancelled_after_shipping/customer_cancelled_after_shipping_bloc.dart';
import 'package:swadesic/features/seller/seller_all_orders/seller_all_orders_common_widget.dart';
import 'package:swadesic/features/seller/seller_all_orders/seller_sub_order/seller_sub_order_bloc.dart';
import 'package:swadesic/model/order_response/get_order_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:swadesic/model/order_response/sub_order.dart';

class CustomerCancelledAfterShippingScreen extends StatefulWidget {
  final List<SubOrder> suborderList;
  final Order order;
  final SellerSubOrderBloc sellerSubOrderBloc;

  const CustomerCancelledAfterShippingScreen({
    Key? key,
    required this.suborderList,
    required this.order,
    required this.sellerSubOrderBloc,
  }) : super(key: key);

  @override
  State<CustomerCancelledAfterShippingScreen> createState() => _CustomerCancelledAfterShippingScreenState();
}

class _CustomerCancelledAfterShippingScreenState extends State<CustomerCancelledAfterShippingScreen> {
  // region Bloc
  late CustomerCancelledAfterShippingBloc customerCancelledAfterShippingBloc;

  // endregion

  // region Init
  @override
  void initState() {
    customerCancelledAfterShippingBloc = CustomerCancelledAfterShippingBloc(context, widget.suborderList, widget.order, widget.sellerSubOrderBloc);
    customerCancelledAfterShippingBloc.init();
    super.initState();
  }
  // endregion


  //region Dis update
  @override
  void didUpdateWidget(covariant CustomerCancelledAfterShippingScreen oldWidget) {
    customerCancelledAfterShippingBloc = CustomerCancelledAfterShippingBloc(context, widget.suborderList, widget.order, widget.sellerSubOrderBloc);
    customerCancelledAfterShippingBloc.init();
    super.didUpdateWidget(oldWidget);
  }
  //endregion






  @override
  Widget build(BuildContext context) {
    // late ExpandableController expandableController = ExpandableController();
    // expandableController.notifyListeners()
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 30, horizontal: 10),
      decoration: BoxDecoration(
          color: AppColors.appWhite,
          border: Border(
              bottom: BorderSide(
                  color: AppColors.lightStroke
              )
          )
      ),
      child: ExpandablePanel(
        //region Theme
        theme: ExpandableThemeData(
          animationDuration: Duration(microseconds: 100),
          iconPlacement: ExpandablePanelIconPlacement.right,
          // alignment: Alignment.bottomRight
          tapBodyToCollapse: true,
          tapHeaderToExpand: true,
          tapBodyToExpand: true,
          iconPadding: EdgeInsets.zero,
          //useInkWell: false,
          // iconRotationAngle:math.pi/2,
          headerAlignment: ExpandablePanelHeaderAlignment.center,

          iconSize: 40,
          iconColor: AppColors.appBlack,

          // iconPadding: EdgeInsets.symmetric( horizontal: 10),
          //iconColor: Colors.green
        ),
        //endregion

        //Waiting for confirmation
        //region Header
        header: header(),
        //endregion
        collapsed:  needHelpAndHowRefundCalculate(),

        expanded: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            needHelpAndHowRefundCalculate(),
            Container(
              padding: const EdgeInsets.only(top: 30),
              color: AppColors.appWhite,
              child: ListView.builder(
                  physics: const NeverScrollableScrollPhysics(),
                  shrinkWrap: true,
                  itemCount: customerCancelledAfterShippingBloc.suborderList.length,
                  itemBuilder: (context, index) {
                    return Column(
                      children: [
                        Column(
                          mainAxisSize: MainAxisSize.min,
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            verticalSizedBox(10),
                            Text(
                                "Cancelled on: ${customerCancelledAfterShippingBloc.suborderList[index].cancelledDate == null ? '' : CommonMethods.convertStringDateTimeSlashFormat(customerCancelledAfterShippingBloc.suborderList[index].cancelledDate!)}",
                                maxLines: 2,
                                overflow: TextOverflow.visible,
                                textAlign: TextAlign.left,
                                style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack,)
                            ),


                            productInfoCard(context: context, subOrder: customerCancelledAfterShippingBloc.suborderList[index],
                            ),
                            //Reason
                            Text("${AppStrings.reason}: ${customerCancelledAfterShippingBloc.suborderList[index].cancellationReason??" "}",
                            textAlign: TextAlign.left,
                            style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack,),
                            ),
                            verticalSizedBox(10)

                          ],
                        ),
                        //Divider
                        Visibility(
                          visible: customerCancelledAfterShippingBloc.suborderList.length-1 != index,
                          child: Container(
                            color: AppColors.appWhite,
                            child: Padding(
                              padding: const EdgeInsets.symmetric(vertical: 10),
                              child: divider(),
                            ),
                          ),
                        ),
                      ],
                    );
                  }),
            ),
          ],
        ),
      ),
    );
  }

  //region Header
  Widget header() {
    return SellerAllOrdersCommonWidgets.sellerCommonComponent(
      icon: AppImages.packageIcon,
      componentName: AppStrings.customerCancelledAfterShipping,
      suborderList: customerCancelledAfterShippingBloc.suborderList,
      isEstimateDeliveryShow: false,
      additionalWidgets:
      Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            alignment: Alignment.centerLeft,
            child: Text(AppStrings.customerCancelledTheBelowOrder,style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),),
          ),
        ],
      ),

    );
  }

  //endregion


  //region Need help and how refund amount calculate
  Widget needHelpAndHowRefundCalculate() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Row(
        //   mainAxisSize: MainAxisSize.min,
        //   mainAxisAlignment: MainAxisAlignment.start,
        //   crossAxisAlignment: CrossAxisAlignment.center,
        //   children: [
        //     Expanded(
        //         child: AppCommonWidgets.inActiveButton(
        //             buttonName: AppStrings.needHelp,
        //             onTap: () {
        //               CommonMethods.reportAndSuggestion(context: context);
        //
        //             })),
        //   ],
        // ),
        // verticalSizedBox(20),
        InkWell(
            onTap: (){
              // cancelledByYouBeforeShippingBloc.onTapHowRefundCalculated();
            },
            child: SellerAllOrdersCommonWidgets.howRefundAmountCalculated(subOrderList: customerCancelledAfterShippingBloc.suborderList, sellerSubOrderBloc: customerCancelledAfterShippingBloc.sellerSubOrderBloc))
      ],
    );
  }

//endregion
}
