import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

class UserReferrerScreen extends StatefulWidget {
  final String referralCode;

  const UserReferrerScreen({Key? key, required this.referralCode}) : super(key: key);

  @override
  _UserReferrerScreenState createState() => _UserReferrerScreenState();
}

class _UserReferrerScreenState extends State<UserReferrerScreen> with SingleTickerProviderStateMixin {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: LayoutBuilder(

        builder: (BuildContext context, BoxConstraints constraints) {
          return Stack(
            alignment: Alignment.center,
            fit: StackFit.expand,
            children: [
              Image.asset(
                AppImages.onboardingBackground,
                fit: BoxFit.fitWidth,
              ),
              Container(
                  margin: EdgeInsets.only(left: 20,right: 20,bottom: constraints.maxWidth * 0.08,top:constraints.maxWidth * 0.4 ),


                  child: body()),
            ],
          );
        },
      ),
    );
  }

  //region Body
  Widget body() {
    return Column(
      children: [
        appLogo(),
        description(),
        appLogoWithName(),
        const Expanded(child: SizedBox()),
        footer(),
        referralCode(),
      ],
    );
  }

  //endregion

  //region App logo
  Widget appLogo() {
    return LayoutBuilder(
      builder: (BuildContext context, BoxConstraints constraints) {
        return SizedBox(
          height: constraints.maxWidth / 2,
          width: constraints.maxWidth / 2,
          child: Image.asset(
            AppImages.splashLogo,
            fit: BoxFit.contain,
          ),
        ); // Replace with your desired widget
      },
    );
  }

  //endregion

  //region Description
  Widget description() {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 16),
      child: Text(
        "You have been invited to\nSwadeshi first Marketplace Social Network",
        textAlign: TextAlign.center,
        style: AppTextStyle.introSlideDetail(textColor: AppColors.borderColor0),
      ),
    );
  }

  //endregion

//region A nation
  Widget aNation() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 30),
      child: Text(
        AppStrings.aNation,
        textAlign: TextAlign.center,
        style: TextStyle(
            letterSpacing: 0.3,
            height: 1.5,
            fontFamily: AppConstants.leagueSemiBold,
            fontSize: 23,
            fontWeight: FontWeight.w600,
            // fontSize:  CommonMethods.fontSizeChanger(fontSize: 14),

            color: AppColors.appBlack),
      ),
    );
  }

//endregion

//region App logo with name
  Widget appLogoWithName() {
    return Column(
      children: [
        SizedBox(
            height: MediaQuery.of(context).size.height * 0.04,
            child: Image.asset(
              AppImages.appName,
              fit: BoxFit.fitWidth,
            )),
        const SizedBox(height: 5,),
        Text("Assembling all Swadeshi brands & consumers!",style: AppTextStyle.robotoMonoBold(textColor: AppColors.appBlack),),
      ],
    );
  }
//endregion



//region Footer
Widget footer(){
    return Column(
      children: [
        CupertinoButton(
          padding: EdgeInsets.zero,
            onPressed: (){
              CommonMethods.openUrl(url: "https://play.google.com/store/apps/details?id=com.sociallyx.swadesic&referrer=${widget.referralCode}");
            },
            child: Image.asset(AppImages.getOnPlayStore,height: 60,)),
        const SizedBox(height: 50,),
        Text("By joining through an invite, you will be receiving ₹50 worth infinity points which you can use for purchases and ₹200 if you are a business.",

          textAlign: TextAlign.center,
          style: AppTextStyle.introSlideTitle(textColor: AppColors.appBlack).copyWith(fontSize: 13),
        )
      ],
    );
}
//endregion



//region referral code
Widget referralCode(){
    return Container(
      margin: const EdgeInsets.only(top:30),
      child: CupertinoButton(
        padding: EdgeInsets.zero,
        onPressed: () {
          CommonMethods.copyText(context,widget.referralCode);
        },
        child: Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(
              "${widget.referralCode.toUpperCase()}",style: AppTextStyle.introSlideDetail(textColor: AppColors.borderColor0),
            ),
            const SizedBox(width: 10,),
            Icon(Icons.file_copy,size: 25,color: AppColors.appBlack,)
          ],
        ),
      ),
    );
}
//endregion

}
