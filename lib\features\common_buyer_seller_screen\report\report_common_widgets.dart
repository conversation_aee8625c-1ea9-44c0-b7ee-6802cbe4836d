import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/common_buyer_seller_screen/report/report_bloc.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_widgets.dart';

class ReportCommonWidgets {


  //region Report radio
  static Widget reportRadio({required String reason, required ReportBloc reportBloc, required bool isSelected}) {
    return Container(
      padding: const EdgeInsets.all(10),
      decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(
              color: AppColors.borderColor1, // Border color
              width: 1.0, // Border width
            ),
          )),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SvgPicture.asset(isSelected ? AppImages.circularRadioTrue : AppImages.circularRadioFalse),
          horizontalSizedBox(10),
          Expanded(child:
          Text(reason, style: AppTextStyle.contentText0(textColor: AppColors.appBlack),)
            // appText(reason,maxLine: 2,
            // fontWeight: FontWeight.w400,
            //   fontSize: 15,
            //   fontFamily: AppConstants.rRegular,
            //   color: AppColors.appBlack,
            //   height: 1.11
            //
            // ),
          )
        ],
      ),


    );
  }

  //endregion


//region report text field
  static Widget reportTextField(
      {required TextEditingController textCtrl, required String hintText, maxLine = 1, minLine = 1, required dynamic onChange}) {
    return TextFormField(
      keyboardType: TextInputType.text,
      textInputAction: TextInputAction.done,
      maxLines: maxLine,
      minLines: minLine,
      controller: textCtrl,
      onChanged: (value) {
        onChange();
      },
      textCapitalization: TextCapitalization.sentences,
      style: TextStyle(fontFamily: AppConstants.rRegular, fontSize: 15, fontWeight: FontWeight.w400, color: AppColors.appBlack),
      inputFormatters: [
        LengthLimitingTextInputFormatter(100),
      ],
      decoration: InputDecoration(
        filled: true,
        contentPadding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
        fillColor: AppColors.textFieldFill1,
        isDense: true,
        hintText: hintText,
        hintStyle: TextStyle(
            fontSize: 15,
            fontWeight: FontWeight.w400,
            color: AppColors.writingColor3
        ),

        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
          borderSide: BorderSide(color: AppColors.textFieldFill1, width: 1.5),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
          borderSide: BorderSide(color: AppColors.textFieldFill1, width: 1.5),
        ),

      ),
    );
  }

//endregion


}