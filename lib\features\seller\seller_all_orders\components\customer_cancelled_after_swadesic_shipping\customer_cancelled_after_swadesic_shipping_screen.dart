import 'package:expandable/expandable.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/seller/seller_all_orders/components/customer_cancelled_after_swadesic_shipping/customer_cancelled_after_swadesic_shipping_bloc.dart';
import 'package:swadesic/features/seller/seller_all_orders/seller_all_orders_common_widget.dart';
import 'package:swadesic/features/seller/seller_all_orders/seller_sub_order/seller_sub_order_bloc.dart';
import 'package:swadesic/model/order_response/get_order_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:swadesic/model/order_response/sub_order.dart';

class CustomerCancelledAfterSwadesicShippingScreen extends StatefulWidget {
  final List<SubOrder> suborderList;
  final Order order;
  final SellerSubOrderBloc sellerSubOrderBloc;

  const CustomerCancelledAfterSwadesicShippingScreen({
    Key? key,
    required this.suborderList,
    required this.order,
    required this.sellerSubOrderBloc,
  }) : super(key: key);

  @override
  State<CustomerCancelledAfterSwadesicShippingScreen> createState() =>
      _CustomerCancelledAfterSwadesicShippingScreenState();
}

class _CustomerCancelledAfterSwadesicShippingScreenState
    extends State<CustomerCancelledAfterSwadesicShippingScreen> {
  // region Bloc
  late CustomerCancelledAfterSwadesicShippingBloc
      customerCancelledAfterShippingBloc;

  // endregion

  // region Init
  @override
  void initState() {
    customerCancelledAfterShippingBloc =
        CustomerCancelledAfterSwadesicShippingBloc(context, widget.suborderList,
            widget.order, widget.sellerSubOrderBloc);
    customerCancelledAfterShippingBloc.init();
    super.initState();
  }
  // endregion

  //region Dis update
  @override
  void didUpdateWidget(
      covariant CustomerCancelledAfterSwadesicShippingScreen oldWidget) {
    customerCancelledAfterShippingBloc =
        CustomerCancelledAfterSwadesicShippingBloc(context, widget.suborderList,
            widget.order, widget.sellerSubOrderBloc);
    customerCancelledAfterShippingBloc.init();
    super.didUpdateWidget(oldWidget);
  }
  //endregion

  //region Build
  @override
  Widget build(BuildContext context) {
    return body();
  }
  //endregion

  //region Body
  Widget body() {
    return ListView.builder(
      physics: const NeverScrollableScrollPhysics(),
      shrinkWrap: true,
      itemCount: customerCancelledAfterShippingBloc.groupNameList.length,
      itemBuilder: (context, index) {
        return customerCancelledAfterShipping(
            groupName: customerCancelledAfterShippingBloc.groupNameList[index]);
      },
    );
  }
  //endregion

  //region Customer Cancelled After Shipping
  Widget customerCancelledAfterShipping({required String groupName}) {
    List<SubOrder> groupedSuborderList = [];

    ///Add all suborders to the suborder list as per the display package number
    groupedSuborderList = customerCancelledAfterShippingBloc.suborderList
        .where((element) => element.displayPackageNumber == groupName)
        .toList();

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 30, horizontal: 10),
      decoration: BoxDecoration(
          color: AppColors.appWhite,
          border: Border(bottom: BorderSide(color: AppColors.lightStroke))),
      child: ExpandablePanel(
        //region Theme
        theme: ExpandableThemeData(
          animationDuration: Duration(microseconds: 100),
          iconPlacement: ExpandablePanelIconPlacement.right,
          // alignment: Alignment.bottomRight
          tapBodyToCollapse: true,
          tapHeaderToExpand: true,
          tapBodyToExpand: true,
          iconPadding: EdgeInsets.zero,
          //useInkWell: false,
          // iconRotationAngle:math.pi/2,
          headerAlignment: ExpandablePanelHeaderAlignment.center,

          iconSize: 40,
          iconColor: AppColors.appBlack,

          // iconPadding: EdgeInsets.symmetric( horizontal: 10),
          //iconColor: Colors.green
        ),
        //endregion

        //region Header
        header:
            header(headerOrderList: groupedSuborderList, groupName: groupName),
        //endregion
        collapsed: needHelpAndHowRefundCalculate(),

        expanded: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            needHelpAndHowRefundCalculate(),
            Container(
              padding: const EdgeInsets.only(top: 30),
              color: AppColors.appWhite,
              child: ListView.builder(
                physics: const NeverScrollableScrollPhysics(),
                shrinkWrap: true,
                itemCount: groupedSuborderList.length,
                itemBuilder: (context, index) {
                  return Column(
                    children: [
                      Column(
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          verticalSizedBox(10),
                          Text(
                              "Cancelled on: ${groupedSuborderList[index].cancelledDate == null ? '' : CommonMethods.convertStringDateTimeSlashFormat(groupedSuborderList[index].cancelledDate!)}",
                              maxLines: 2,
                              overflow: TextOverflow.visible,
                              textAlign: TextAlign.left,
                              style: AppTextStyle.contentHeading0(
                                textColor: AppColors.appBlack,
                              )),

                          productInfoCard(
                              context: context,
                              subOrder: groupedSuborderList[index]),

                          //Reason
                          Text(
                            "${AppStrings.reason}: ${groupedSuborderList[index].cancellationReason ?? " "}",
                            textAlign: TextAlign.left,
                            style: AppTextStyle.contentHeading0(
                              textColor: AppColors.appBlack,
                            ),
                          ),

                          // Return status
                          Visibility(
                            visible:
                                groupedSuborderList[index].suborderNumber !=
                                    null,
                            child: Container(
                              margin: const EdgeInsets.only(top: 10),
                              child: Text(
                                "Return status: ${customerCancelledAfterShippingBloc.isProductReturnAccepted(groupedSuborderList[index].suborderNumber ?? '') ? 'Returns accepted' : 'No returns accepted'}",
                                textAlign: TextAlign.left,
                                style: AppTextStyle.contentHeading0(
                                  textColor: customerCancelledAfterShippingBloc
                                          .isProductReturnAccepted(
                                              groupedSuborderList[index]
                                                      .suborderNumber ??
                                                  '')
                                      ? AppColors.brandBlack
                                      : AppColors.orange,
                                ),
                              ),
                            ),
                          ),

                          // Status for this product
                          Builder(builder: (context) {
                            // Check if this suborder has a secondary status
                            String? secondaryStatus;
                            if (groupedSuborderList[index]
                                        .secondarySuborderStatus !=
                                    null &&
                                groupedSuborderList[index]
                                    .secondarySuborderStatus!
                                    .isNotEmpty) {
                              secondaryStatus = groupedSuborderList[index]
                                  .secondarySuborderStatus;
                            }

                            return Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // Show status for this product
                                Container(
                                  margin: const EdgeInsets.only(top: 10),
                                  child: Text(
                                    "Status: ${groupedSuborderList[index].suborderStatus}",
                                    textAlign: TextAlign.left,
                                    style: AppTextStyle.contentHeading0(
                                        textColor: AppColors.brandBlack),
                                  ),
                                ),

                                // Show return package details if status is RETURN_CONFIRMED
                                Visibility(
                                  visible:
                                      secondaryStatus == 'RETURN_CONFIRMED' &&
                                          groupedSuborderList[index]
                                                  .displayReturnPackageNumber !=
                                              null,
                                  child: Container(
                                    margin: const EdgeInsets.only(top: 10),
                                    padding: const EdgeInsets.all(10),
                                    decoration: BoxDecoration(
                                      color: AppColors.inActiveGreen
                                          .withOpacity(0.3),
                                      borderRadius: BorderRadius.circular(5),
                                    ),
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          "Return Package: ${groupedSuborderList[index].displayReturnPackageNumber}",
                                          textAlign: TextAlign.left,
                                          style: AppTextStyle.contentHeading0(
                                              textColor: AppColors.appBlack),
                                        ),
                                        verticalSizedBox(5),
                                        if (groupedSuborderList[index]
                                                .returnConfirmationDate !=
                                            null)
                                          Text(
                                            "Return Confirmed on: ${groupedSuborderList[index].returnConfirmationDate}",
                                            textAlign: TextAlign.left,
                                            style: AppTextStyle.contentText0(
                                                textColor: AppColors.appBlack),
                                          ),
                                        verticalSizedBox(5),
                                        if (groupedSuborderList[index]
                                                .estimatedPickupDate !=
                                            null)
                                          Text(
                                            "Estimated Pickup: ${groupedSuborderList[index].estimatedPickupDate}",
                                            textAlign: TextAlign.left,
                                            style: AppTextStyle.contentText0(
                                                textColor: AppColors.appBlack),
                                          ),
                                        verticalSizedBox(5),
                                        if (groupedSuborderList[index]
                                                .returnTrackingNumber !=
                                            null)
                                          Text(
                                            "Tracking Number: ${groupedSuborderList[index].returnTrackingNumber}",
                                            textAlign: TextAlign.left,
                                            style: AppTextStyle.contentText0(
                                                textColor: AppColors.appBlack),
                                          ),
                                        verticalSizedBox(5),
                                        if (groupedSuborderList[index]
                                                    .returnTrackingLink !=
                                                null &&
                                            groupedSuborderList[index]
                                                .returnTrackingLink!
                                                .isNotEmpty)
                                          InkWell(
                                            onTap: () {
                                              CommonMethods.openUrl(
                                                  url:
                                                      groupedSuborderList[index]
                                                          .returnTrackingLink!);
                                            },
                                            child: Text(
                                              "Track Return",
                                              textAlign: TextAlign.left,
                                              style: AppTextStyle.contentText0(
                                                      textColor:
                                                          AppColors.brandBlack)
                                                  .copyWith(
                                                decoration:
                                                    TextDecoration.underline,
                                              ),
                                            ),
                                          ),
                                      ],
                                    ),
                                  ),
                                ),
                              ],
                            );
                          }),
                          verticalSizedBox(10)
                        ],
                      ),
                      //Divider
                      Visibility(
                        visible: groupedSuborderList.length - 1 != index,
                        child: Container(
                          color: AppColors.appWhite,
                          child: Padding(
                            padding: const EdgeInsets.symmetric(vertical: 10),
                            child: divider(),
                          ),
                        ),
                      ),
                    ],
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
  //endregion

  //region Header
  Widget header(
      {required List<SubOrder> headerOrderList, required String groupName}) {
    // Check if any suborder in this package has a secondary status
    String? secondaryStatus;
    if (customerCancelledAfterShippingBloc
        .isPartialCancellationForPackage(groupName)) {
      // Check all suborders in the order for this package
      if (customerCancelledAfterShippingBloc.order.subOrderList != null) {
        for (var suborder
            in customerCancelledAfterShippingBloc.order.subOrderList!) {
          if (suborder.displayPackageNumber == groupName &&
              suborder.secondarySuborderStatus != null &&
              suborder.secondarySuborderStatus!.isNotEmpty) {
            secondaryStatus = suborder.secondarySuborderStatus;
            break;
          }
        }
      }
    }

    // Create component title based on secondary status
    String componentTitle;
    if (customerCancelledAfterShippingBloc
            .isPartialCancellationForPackage(groupName) &&
        secondaryStatus != null &&
        secondaryStatus != "DELIVERY_IN_PROGRESS") {
      // Format: "Secondary status (generalised text) - package number"
      String readableStatus =
          CommonMethods.labelStatusToSentence(input: secondaryStatus) ??
              secondaryStatus;
      componentTitle =
          "$readableStatus - $groupName ${customerCancelledAfterShippingBloc.isPartialCancellationForPackage(groupName) ? '(Partial Cancel)' : '(Full Cancel)'}";
    } else {
      // Use the default title format
      componentTitle =
          "${AppStrings.customerCancelledAfterShipping} :$groupName ${customerCancelledAfterShippingBloc.isPartialCancellationForPackage(groupName) ? '(Partial Cancel)' : '(Full Cancel)'}";
    }

    return SellerAllOrdersCommonWidgets.sellerCommonComponent(
      icon: AppImages.packageIcon,
      componentName: componentTitle,
      suborderList: headerOrderList,
      isEstimateDeliveryShow: false,
      additionalWidgets: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Show current status right below the title
          Container(
            alignment: Alignment.centerLeft,
            margin: const EdgeInsets.only(bottom: 5),
            child: Text(
              "Current status: ${headerOrderList.first.suborderStatus}",
              style:
                  AppTextStyle.contentHeading0(textColor: AppColors.brandBlack),
            ),
          ),

          Container(
            alignment: Alignment.centerLeft,
            child: Text(
              AppStrings.customerCancelledTheBelowOrder,
              style:
                  AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
            ),
          ),

          verticalSizedBox(10),
          // Cancellation message based on isPartialCancel
          Container(
            alignment: Alignment.centerLeft,
            padding: const EdgeInsets.all(10),
            decoration: BoxDecoration(
              color: customerCancelledAfterShippingBloc
                      .isPartialCancellationForPackage(groupName)
                  ? AppColors.inActiveGreen.withOpacity(0.3)
                  : AppColors.orange.withOpacity(0.2),
              borderRadius: BorderRadius.circular(5),
            ),
            child: Text(
              customerCancelledAfterShippingBloc
                  .getCancellationMessage(groupName),
              style: AppTextStyle.contentText0(
                  textColor: customerCancelledAfterShippingBloc
                          .isPartialCancellationForPackage(groupName)
                      ? AppColors.appBlack
                      : AppColors.orange),
            ),
          ),
        ],
      ),
    );
  }

  //endregion

  //region Need help and how refund amount calculate
  Widget needHelpAndHowRefundCalculate() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Row(
        //   mainAxisSize: MainAxisSize.min,
        //   mainAxisAlignment: MainAxisAlignment.start,
        //   crossAxisAlignment: CrossAxisAlignment.center,
        //   children: [
        //     Expanded(
        //         child: AppCommonWidgets.inActiveButton(
        //             buttonName: AppStrings.needHelp,
        //             onTap: () {
        //               CommonMethods.reportAndSuggestion(context: context);
        //
        //             })),
        //   ],
        // ),
        // verticalSizedBox(20),
        InkWell(
            onTap: () {
              // cancelledByYouBeforeShippingBloc.onTapHowRefundCalculated();
            },
            child: SellerAllOrdersCommonWidgets.howRefundAmountCalculated(
                subOrderList: customerCancelledAfterShippingBloc.suborderList,
                sellerSubOrderBloc:
                    customerCancelledAfterShippingBloc.sellerSubOrderBloc))
      ],
    );
  }

//endregion
}
