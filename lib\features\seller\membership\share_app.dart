import 'package:flutter/material.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

class ShareMembershipAppButton extends StatelessWidget {
  const ShareMembershipAppButton({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: (){
        CommonMethods.share("https://swadesic.com");
        // membershipBloc.createInviteCode(friendsInfo.phonenumber!,"SELLER",friendsInfo.name);
      },
      child: Container(
        width: double.infinity,
        alignment: Alignment.center,
        margin: const EdgeInsets.symmetric(vertical: 10),
        padding: const EdgeInsets.symmetric(vertical: 15),
        decoration: BoxDecoration(
            color: AppColors.textFieldFill1,
            borderRadius: BorderRadius.all(Radius.circular(10))
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(Icons.share_rounded,color: AppColors.darkGray,),
            // SvgPicture.asset(AppImages.shareIcon),
            horizontalSizedBox(10),
            Text(AppStrings.shareApp,
              style:  TextStyle(
                fontFamily: "LatoBold",
                fontSize: 15,
                fontWeight: FontWeight.w700,
                color:AppColors.writingColor2,
              ),
            ),


          ],
        ),
      ),
    );
  }
}
