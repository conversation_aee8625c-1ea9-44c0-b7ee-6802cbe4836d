import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/mobile_number_otp/intro_slider/intro_slider_bloc.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';

class IntroSliderBottomSheet extends StatefulWidget {
  const IntroSliderBottomSheet({super.key});

  @override
  State<IntroSliderBottomSheet> createState() => _IntroSliderBottomSheetState();
}

class _IntroSliderBottomSheetState extends State<IntroSliderBottomSheet> {
  late IntroSliderBloc introSliderBloc;

  @override
  void initState() {
    introSliderBloc = IntroSliderBloc(context);
    introSliderBloc.init();
    super.initState();
  }

  @override
  void dispose() {
    introSliderBloc.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.95,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Column(
        children: [
          // Handle bar
          Container(
            margin: const EdgeInsets.only(top: 10),
            height: 4,
            width: 40,
            decoration: BoxDecoration(
              color: AppColors.appWhite,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(height: 60),
          // App icon
          // bharatFlag(),
          // Page slider content
          Expanded(
            child: Column(
              children: [
                Expanded(child: pageSliderTitleAndDesc()),
                // Dot indicators
                StreamBuilder<int>(
                  stream: introSliderBloc.currentPageCtrl.stream,
                  initialData: 0,
                  builder: (context, snapshot) {
                    return Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: List<Widget>.generate(
                        introSliderBloc.introSliderList.length,
                        (index) => Container(
                          width: 8,
                          height: 8,
                          margin: const EdgeInsets.symmetric(
                              horizontal: 4, vertical: 16),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(4),
                            color: snapshot.data == index
                                ? AppColors.brandBlack
                                : Colors.grey[300],
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
          // Next button
          // nextButton(),
          const SizedBox(height: 40),
          // App logo
          // appLogoWithName(),
          // const SizedBox(height: 20),
        ],
      ),
    );
  }

  Widget bharatFlag() {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 20),
      child: Image.asset(
        AppImages.appIcon,
        height: 60,
        width: 60,
      ),
    );
  }

  Widget pageSliderTitleAndDesc() {
    return PageView.builder(
      controller: introSliderBloc.pageController,
      onPageChanged: (pageNumber) {
        introSliderBloc.onChangePage(pageNumber: pageNumber);
      },
      itemCount: introSliderBloc.introSliderList.length,
      itemBuilder: (context, index) {
        return SingleChildScrollView(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                introSliderBloc.introSliderList[index].title,
                textAlign: TextAlign.left,
                style: AppTextStyle.pageHeadingBold(
                    textColor: AppColors.brandBlack),
              ),
              const SizedBox(height: 30),
              RichText(
                textAlign: TextAlign.left,
                text: introSliderBloc.introDetail(
                    introSliderBloc.introSliderList[index].description),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget nextButton() {
    return LayoutBuilder(
      builder: (BuildContext context, BoxConstraints constraints) {
        double width = constraints.maxWidth;
        return Container(
          margin: const EdgeInsets.only(top: 20, left: 20, right: 20),
          child: StreamBuilder<int>(
            stream: introSliderBloc.currentPageCtrl.stream,
            builder: (context, snapshot) {
              return CupertinoButton(
                padding: EdgeInsets.zero,
                onPressed: () {
                  introSliderBloc.onTapNext();
                },
                child: AnimatedContainer(
                  height: 60,
                  width: snapshot.data == 3 ? width - 40 : 60,
                  duration: const Duration(milliseconds: 500),
                  decoration: BoxDecoration(
                    color: AppColors.brandBlack,
                    borderRadius: BorderRadius.circular(100),
                    shape: BoxShape.rectangle,
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      StreamBuilder<bool>(
                        stream: introSliderBloc.visibleButtonText.stream,
                        initialData: false,
                        builder: (context, textVisibleSnapshot) {
                          return Visibility(
                            visible: textVisibleSnapshot.data!,
                            child: Padding(
                              padding:
                                  const EdgeInsets.only(right: 20, left: 20),
                              child: Text(
                                AppStrings.letsMakeGreatAgain,
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                                style: AppTextStyle.exButton(
                                    textColor: AppColors.appWhite),
                              ),
                            ),
                          );
                        },
                      ),
                      SvgPicture.asset(
                        AppImages.rightArrow,
                        height: 30,
                        width: 30,
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        );
      },
    );
  }

  Widget appLogoWithName() {
    return StreamBuilder<int>(
      stream: introSliderBloc.currentPageCtrl.stream,
      builder: (context, snapshot) {
        return Container(
          margin: const EdgeInsets.symmetric(vertical: 10),
          child: snapshot.data == 3
              ? const SizedBox(height: 40, width: 40)
              : Row(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Image.asset(
                      AppImages.swadesicLogoWithNamePng,
                      height: 40,
                      width: 160,
                    ),
                  ],
                ),
        );
      },
    );
  }
}
