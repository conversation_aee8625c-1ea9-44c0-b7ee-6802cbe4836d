import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/seller/deactivate_and_delete_store/exit_survey/exit_survey_bloc.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_test_fields/app_text_fields.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/app_title_and_options/app_title_and_options.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

//region ExitSurvey Screen
class ExitSurveyScreen extends StatefulWidget {
  final String leadingTitle;
  final String reference;

  const ExitSurveyScreen(
      {Key? key, required this.reference, required this.leadingTitle})
      : super(key: key);

  @override
  State<ExitSurveyScreen> createState() => _ExitSurveyScreenState();
}
//endregion

class _ExitSurveyScreenState extends State<ExitSurveyScreen> {
  //region Build
  late ExitSurveyBloc exitSurveyBloc;

  //endregion
  //region Init
  @override
  void initState() {
    exitSurveyBloc = ExitSurveyBloc(context, widget.reference);
    exitSurveyBloc.init();
    super.initState();
  }

  //endregion
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.appWhite,
      appBar: appBar(),
      body: SafeArea(child: body()),
    );
  }

  //region Appbar
  AppBar appBar() {
    return AppCommonWidgets.mainAppBar(
      context: context,
      isCustomTitle: false,
      title: widget.leadingTitle,
      isDefaultMenuVisible: true,
      isMembershipVisible: false,
      isCartVisible: false,
    );
  }

  //endregion

  //region Body
  Widget body() {
    return ListView(
      padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 15),
      children: [
        category(),
        verticalSizedBox(20),
        description(),
        verticalSizedBox(20),
        attachment(),
        verticalSizedBox(20),
        subMitAndDelete(),
      ],
    );
  }

//endregion

//region Category
  Widget category() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        AppTitleAndOptions(
          title: AppStrings.category,
          option: AppCommonWidgets.dropDownOptions(
              arrowColor: AppColors.borderColor1,
              onTap: () {
                //buyerOnBoardingBloc.onTapCity();
              },
              context: context,
              hintText: "Account deletion",
              value: ""),
        ),
      ],
    );
  }

//endregion

//region Description
  Widget description() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        AppTitleAndOptions(
          title: AppStrings.description,
          option: AppTextFields.allTextField(
            maxEntry: 500,
            maxLines: 10,
            minLines: 10,
            context: context,
            textInputAction: TextInputAction.newline,
            keyboardType: TextInputType.multiline,
            textEditingController: exitSurveyBloc.descriptionTextCtrl,
            hintText: AppStrings.pleaseBeAsDetailed,
          ),
        ),
      ],
    );
  }
//endregion

//region Attachment
  Widget attachment() {
    return StreamBuilder<ExitSurveyState>(
        stream: exitSurveyBloc.exitSurveyStateCtrl.stream,
        builder: (context, snapshot) {
          return Column(
            children: [
              InkWell(
                  onTap: () {
                    exitSurveyBloc.onTapAddFile();
                  },
                  child: Container(
                    margin: const EdgeInsets.symmetric(vertical: 20),
                    padding: const EdgeInsets.symmetric(
                        horizontal: 10, vertical: 10),
                    decoration: BoxDecoration(
                      color: AppColors.textFieldFill1,
                      borderRadius: BorderRadius.all(Radius.circular(10)),
                    ),
                    child: Row(
                      children: [
                        SvgPicture.asset(
                          AppImages.plus,
                          color: AppColors.writingColor2,
                        ),
                        horizontalSizedBox(10),
                        Text(
                          "Upload screenshots, images, videos or docs",
                          overflow: TextOverflow.ellipsis,
                          style: AppTextStyle.access0(
                              textColor: AppColors.appBlack),
                        ),
                      ],
                    ),
                  )),
              exitSurveyBloc.files.isEmpty
                  ? const SizedBox()
                  : Row(
                      children: [
                        Expanded(
                          child: SizedBox(
                            height: 100,
                            child: ListView.builder(
                                shrinkWrap: true,
                                scrollDirection: Axis.horizontal,
                                itemCount: exitSurveyBloc.files.length,
                                itemBuilder: (context, index) {
                                  return Stack(
                                    alignment: Alignment.topRight,
                                    children: [
                                      Padding(
                                        padding:
                                            const EdgeInsets.only(left: 10),
                                        child: imageAndDocument(
                                            data: exitSurveyBloc.files[index]),
                                      ),
                                      Positioned(
                                          top: 2,
                                          right: 2,
                                          child: InkWell(
                                              onTap: () {
                                                //print(index);
                                                exitSurveyBloc.onTapRemoveFile(
                                                    data: exitSurveyBloc
                                                        .files[index]);
                                              },
                                              child: SvgPicture.asset(
                                                  AppImages.removeCircle)))
                                    ],
                                  );
                                }),
                          ),
                        ),
                        const SizedBox()
                      ],
                    )
            ],
          );
        });
  }
//endregion

  //region Image and document
  Widget imageAndDocument({required File data}) {
    ///Split file name to identify the extension
    String? fileType;
    fileType = CommonMethods.returnExtension(file: data);
    if (exitSurveyBloc.otherFileTypeList.contains(fileType)) {
      return Container(
          width: 100,
          height: 100,
          color: AppColors.textFieldFill1,
          child: const Icon(Icons.file_copy));
    }
    return SizedBox(
        width: 100,
        height: 100,
        child: Image.file(
          data,
          cacheHeight: 800,
          cacheWidth: 800,
          fit: BoxFit.cover,
        ));
  }
//endregion

//region Submit and delete
  Widget subMitAndDelete() {
    return CupertinoButton(
      onPressed: () {
        exitSurveyBloc.addFeedback();
      },
      padding: EdgeInsets.zero,
      child: Container(
        alignment: Alignment.center,
        padding: const EdgeInsets.symmetric(vertical: 10),
        decoration: BoxDecoration(
            color: AppColors.brandBlack,
            borderRadius: BorderRadius.circular(10)),
        child: Text(
          AppStrings.subMitAndDelete,
          style: AppTextStyle.access0(textColor: AppColors.appWhite),
        ),
      ),
    );
  }
//endregion
}
