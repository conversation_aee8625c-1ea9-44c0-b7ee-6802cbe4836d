import 'package:flutter/material.dart';
import 'package:swadesic/features/seller/seller_all_orders/components/additional_detail/additional_detail_bloc.dart';
import 'package:swadesic/features/seller/seller_all_orders/components/change_order_status/change_order_status_bloc.dart';
import 'package:swadesic/features/seller/seller_all_orders/seller_sub_order/seller_sub_order_bloc.dart';
import 'package:swadesic/model/order_response/get_order_response.dart';
import 'package:swadesic/model/order_response/sub_order.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';

class ChangeOrderStatus extends StatefulWidget {
  final BuildContext previousContext;
  final SellerSubOrderBloc sellerSubOrderBloc;
  final Order order;
  final List<SubOrder> subOrderList;
  final AdditionalDetailBloc additionalDetailBloc;
  const ChangeOrderStatus({Key? key, required this.sellerSubOrderBloc, required this.order, required this.subOrderList, required this.previousContext, required this.additionalDetailBloc}) : super(key: key);

  @override
  State<ChangeOrderStatus> createState() => _ChangeOrderStatusState();
}

class _ChangeOrderStatusState extends State<ChangeOrderStatus> {
  //region Bloc
  late ChangeOrderStatusBloc changeOrderStatusBloc;
  //endregion
  //region Init
  @override
  void initState() {
    changeOrderStatusBloc = ChangeOrderStatusBloc(context,widget.sellerSubOrderBloc,widget.order,widget.subOrderList,widget.previousContext,widget.additionalDetailBloc);
    changeOrderStatusBloc.init();
    super.initState();
  }
  //endregion
  @override
  Widget build(BuildContext context) {

    return body();
  }


  //region Body
Widget body(){
    return Column(
      mainAxisSize: MainAxisSize.min,

      children: [
        changeOrderStatus(),
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 30),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              option(optionText: AppStrings.cancelTheOrder,onTap: (){
                Navigator.pop(widget.previousContext);

                changeOrderStatusBloc.additionalDetailBloc.onTapCancel();

              } ),
              option(optionText: AppStrings.confirmTheOrderAgain,onTap: (){
                Navigator.pop(widget.previousContext);

                changeOrderStatusBloc.additionalDetailBloc.onTapConfirmOrderAgain();

              } ),

            ],
          ),
        ),
      ],
    );
}
//endregion

  //region Change order status
  Widget changeOrderStatus(){
    return Container(
      margin: const EdgeInsets.only(top: 20),
      child: Text(AppStrings.changeOrderStatusTo,style: AppTextStyle.sectionHeading(textColor: AppColors.appBlack),),
    );
  }
  //endregion


//region Option
Widget option({required String optionText,required dynamic onTap}){
    return Column(
      children: [
        InkWell(
          onTap: (){
            onTap();
          },
          child: Container(
            alignment: Alignment.centerLeft,
            width: double.infinity,
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(
                  color: AppColors.textFieldFill1
                )
              )
            ),
            padding: const EdgeInsets.symmetric(vertical: 15),
            child:Text(optionText,style: AppTextStyle.contentText0(textColor: AppColors.appBlack),) ,
          ),
        ),
      ],
    );
}
//endregion

}
