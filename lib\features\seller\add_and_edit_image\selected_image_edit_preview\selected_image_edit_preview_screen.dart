import 'dart:io';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:image_picker/image_picker.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/data_model/app_config_data_model/app_config_data_model.dart';
import 'package:swadesic/features/seller/add_and_edit_image/selected_image_edit_preview/selected_image_edit_preview_bloc.dart';
import 'package:swadesic/model/store_product_response/store_product_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:reorderables/reorderables.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

// region Selected Image Edit PreviewScreen
class SelectedImageEditPreviewScreen extends StatefulWidget {
  //final ProductImageResponse? productImageResponse;
  final String? productReference;

  const SelectedImageEditPreviewScreen({Key? key, this.productReference})
      : super(key: key);

  @override
  _SelectedImageEditPreviewScreenState createState() =>
      _SelectedImageEditPreviewScreenState();
}
// endregion

class _SelectedImageEditPreviewScreenState
    extends State<SelectedImageEditPreviewScreen> {
  // region Bloc
  late SelectedImageEditPreviewBloc selectedImageEditPreviewBloc;

  // endregion

  // region Init
  @override
  void initState() {
    // Hide bottom navigation bar using the app's global controller
    AppConstants.bottomNavigationRefreshCtrl.sink.add(false);
    selectedImageEditPreviewBloc =
        SelectedImageEditPreviewBloc(context, widget.productReference);
    selectedImageEditPreviewBloc.init();
    super.initState();
  }
  // endregion

  //region Dispose
  @override
  void dispose() {
    // Show bottom navigation bar again when leaving the screen
    AppConstants.bottomNavigationRefreshCtrl.sink.add(true);
    selectedImageEditPreviewBloc.dispose();
    super.dispose();
  }
  //endregion

  // region build
  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvoked: (didPop) async {
        if (!didPop) {
          final shouldPop = await selectedImageEditPreviewBloc.onWillPop();
          if (shouldPop && context.mounted) {
            Navigator.of(context).pop();
          }
        }
      },
      child: Scaffold(
        backgroundColor: AppColors.appBlack,
        resizeToAvoidBottomInset: false,
        body: body(),
      ),
    );
  }

  // endregion

  //region App bar
  AppBar appBar() {
    return AppCommonWidgets.mainAppBar(
      backgroundColor: Colors.transparent,
      leadingIconColor: AppColors.appWhite,
      isCustomTitle: false,
      title: "",
      context: context,
      isCenterTitle: false,
      isMembershipVisible: false,
      isCartVisible: false,
      isDefaultMenuVisible: false,
      isCustomMenuVisible: true,
      customMenuButton: StreamBuilder<SelectedImageEditPreviewState>(
          stream:
              selectedImageEditPreviewBloc.selectedImageEditPreviewCtrl.stream,
          initialData: SelectedImageEditPreviewState.Loading,
          builder: (context, snapshot) {
            //Success
            if (snapshot.data == SelectedImageEditPreviewState.Success) {
              // Show tick button if there are unsaved changes (new images or reorder)
              if (selectedImageEditPreviewBloc.hasUnsavedChanges) {
                return CupertinoButton(
                    child: Icon(
                      Icons.check,
                      color: AppColors.appWhite,
                      size: 24,
                    ),
                    onPressed: () {
                      // Save all changes (upload + reorder if needed)
                      selectedImageEditPreviewBloc.saveAllChanges();
                    });
              } else {
                // Show done button when no changes
                return CupertinoButton(
                    child: SvgPicture.asset(
                      AppImages.done,
                      color: AppColors.appWhite,
                      fit: BoxFit.fill,
                    ),
                    onPressed: () {
                      Navigator.pop(context);
                    });
              }
            }
            return const SizedBox();
          }),
    );
  }

  //endregion

  //region AppBar
  // AppBar appBar(){
  //   return AppBar(
  //     backgroundColor: AppColors.appWhite,
  //     leading: CupertinoButton(
  //         onPressed: (){
  //           Navigator.pop(context);
  //         },
  //         padding: EdgeInsets.zero,
  //         child: SvgPicture.asset(AppImages.backButton,color: AppColors.appBlack,fit: BoxFit.fill)),
  //     elevation: 0,
  //     centerTitle: false,
  //     titleSpacing: 0,
  //     title:AppCommonWidgets.appBarTitleText(text: AppStrings.productImage),
  //     automaticallyImplyLeading: false,
  //     //region Done Button
  //     actions: [
  //       CupertinoButton(
  //           child: SvgPicture.asset(AppImages.done,color: AppColors.appBlack,fit: BoxFit.fill,),
  //           onPressed: (){
  //             // widget.addProductToSelectedImage!.isEmpty?
  //             // selectedImageEditPreviewBloc.goBackToAddProduct():
  //             selectedImageEditPreviewBloc.imageReorderApi();
  //             //Navigator.pop(context);
  //             // selectedImageEditPreviewBloc.goBackToAddProduct();
  //
  //           }),
  //       // CupertinoButton(
  //       //     onPressed: (){},
  //       //     child: SvgPicture.asset(AppImages.drawerIcon,color: AppColors.appBlack,height: 24,)),
  //
  //
  //     ],
  //
  //     //endregion
  //   );
  // }

  //endregion

  // region Body
  Widget body() {
    return StreamBuilder<SelectedImageEditPreviewState>(
        stream:
            selectedImageEditPreviewBloc.selectedImageEditPreviewCtrl.stream,
        initialData: SelectedImageEditPreviewState.Loading,
        builder: (context, snapshot) {
          if (snapshot.data == SelectedImageEditPreviewState.Loading) {
            return Center(
              child: AppCommonWidgets.appCircularProgress(),
            );
          }
          return Stack(
            children: [
              productImage(),
              Positioned(
                left: 0,
                right: 0,
                top: 0,
                child: StreamBuilder<bool>(
                    stream: selectedImageEditPreviewBloc
                        .hideAndVisibleAppBarCtrl.stream,
                    initialData: true, // Start with app bar visible
                    builder: (context, snapshot) {
                      return AnimatedContainer(
                        duration: const Duration(milliseconds: 300),
                        height: snapshot.data! ? kToolbarHeight + 30 : 0,
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                            colors: [
                              Colors.black.withOpacity(0.7),
                              Colors.black.withOpacity(0.3),
                              Colors.transparent,
                            ],
                          ),
                        ),
                        child: appBar(),
                      );
                    }),
              ),
              Positioned(
                  bottom: 0,
                  left: 0,
                  right: 0,
                  child: Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Colors.transparent,
                          Colors.black.withOpacity(0.3),
                          Colors.black.withOpacity(0.7),
                        ],
                      ),
                    ),
                    padding: const EdgeInsets.only(bottom: 20, top: 20),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [dotes(), smallImage()],
                    ),
                  ))
            ],
          );
        });
  }

  // endregion

  //region Product images
  Widget productImage() {
    return Container(
      alignment: Alignment.bottomCenter,
      width: MediaQuery.of(context).size.width,
      child: StreamBuilder<bool>(
          stream: selectedImageEditPreviewBloc.gridViewRefreshCtrl.stream,
          initialData: false,
          builder: (context, snapshot) {
            if (selectedImageEditPreviewBloc.unifiedImageList.isEmpty) {
              return Container();
            }

            return PageView.builder(
                controller: selectedImageEditPreviewBloc.pageController,
                onPageChanged: (index) {
                  selectedImageEditPreviewBloc.onChangeSlider(index);
                },
                itemCount: selectedImageEditPreviewBloc.unifiedImageList.length,
                physics: const AlwaysScrollableScrollPhysics(),
                itemBuilder: (context, index) {
                  var imageItem = selectedImageEditPreviewBloc.unifiedImageList[index];

                  return GestureDetector(
                    onTap: () {
                      selectedImageEditPreviewBloc.onTapImage();
                    },
                    child: imageItem is ProdImages
                        ? extendedImage(
                            imageItem.productImage!,
                            context,
                            1000,
                            1000,
                            fit: BoxFit.fitWidth,
                            isFromImagePreview: true,
                            customPlaceHolder: AppImages.productPlaceHolder)
                        : Image.file(
                            File((imageItem as XFile).path),
                            fit: BoxFit.fitWidth,
                          ),
                  );
                });
          }),
    );
  }

  //endregion

  //region Dots
  Widget dotes() {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 5),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.3),
        borderRadius: BorderRadius.circular(20),
      ),
      height: 22,
      child: StreamBuilder<int>(
          stream: selectedImageEditPreviewBloc.sliderCtrl.stream,
          initialData: 0,
          builder: (context, snapshot) {
            if (selectedImageEditPreviewBloc.unifiedImageList.isEmpty) {
              return Container();
            }

            return ListView.builder(
                itemCount: selectedImageEditPreviewBloc.unifiedImageList.length,
                scrollDirection: Axis.horizontal,
                shrinkWrap: true,
                itemBuilder: (context, index) {
                  return Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 5),
                    child: SvgPicture.asset(
                      AppImages.dot,
                      height: 5.29,
                      width: 5.29,
                      color: snapshot.data == index
                          ? AppColors.appWhite
                          : AppColors.appWhite.withOpacity(0.5),
                    ),
                  );
                });
          }),
    );
  }

  //endregion

  // region Drag and reorder
  Widget dragReorderText() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Text(
        AppStrings.dragAndReorder,
        style: const TextStyle(
          fontFamily: "LatoRegular",
          fontSize: 16,
          fontWeight: FontWeight.w400,
          // color: AppColors.appBlack.withOpacity(70),
        ),
      ),
    );
  }

  //endregion

  //region Small image
  Widget smallImage() {
    return StreamBuilder<bool>(
        stream: selectedImageEditPreviewBloc.hideAndVisibleAppBarCtrl.stream,
        initialData: true, // Start with bottom image row visible
        builder: (context, snapshot) {
          if (selectedImageEditPreviewBloc.productImageResponse == null ||
              selectedImageEditPreviewBloc
                  .productImageResponse!.data!.isEmpty) {
            return Container();
          }

          return SafeArea(
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 200),
              height:
                  snapshot.data! ? MediaQuery.of(context).size.height / 10 : 0,
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.2),
              ),
              child: snapshot.data!
                  ? ReorderableListView.builder(
                      scrollDirection: Axis.horizontal,
                      padding: const EdgeInsets.symmetric(horizontal: 8),
                      itemCount:
                          selectedImageEditPreviewBloc.unifiedImageList.length +
                              1, // +1 for add button
                      onReorder: (oldIndex, newIndex) {
                        setState(() {
                          // Adjust indices because add button is at index 0
                          if (oldIndex == 0 || newIndex == 0) {
                            return; // Don't allow reordering the add button
                          }

                          int actualOldIndex = oldIndex - 1;
                          int actualNewIndex = newIndex - 1;

                          // Use the unified list reorder method
                          selectedImageEditPreviewBloc.reorderUnifiedList(
                              actualOldIndex, actualNewIndex);
                        });
                      },
                      itemBuilder: (context, index) {
                        // Add button at index 0
                        if (index == 0) {
                          return Consumer<AppConfigDataModel>(
                            key: const ValueKey('add_button'),
                            builder: (BuildContext context,
                                AppConfigDataModel value, Widget? child) {
                              return Container(
                                margin: const EdgeInsets.only(right: 8),
                                child: InkWell(
                                  onTap: () {
                                    if ((selectedImageEditPreviewBloc
                                            .productImageResponse!
                                            .data!
                                            .length) >=
                                        (value.appConfig!.productImageLimit)) {
                                      return CommonMethods.toastMessage(
                                          "${AppStrings.youCantAddMoreThen} ${value.appConfig!.productImageLimit} images",
                                          context);
                                    }
                                    selectedImageEditPreviewBloc
                                        .goToSelectEditImageScreen();
                                  },
                                  child: SizedBox(
                                    height:
                                        MediaQuery.of(context).size.height / 10,
                                    width:
                                        MediaQuery.of(context).size.height / 10,
                                    child: Container(
                                      decoration: BoxDecoration(
                                          color: Colors.black.withOpacity(0.3),
                                          border: Border.all(
                                              color: AppColors.appWhite
                                                  .withOpacity(0.5),
                                              width: 1),
                                          borderRadius: const BorderRadius.all(
                                              Radius.circular(4))),
                                      child: Center(
                                        child: Icon(Icons.add,
                                            color: AppColors.appWhite,
                                            size: 24),
                                      ),
                                    ),
                                  ),
                                ),
                              );
                            },
                          );
                        }

                        // Image items (index - 1 because add button takes index 0)
                        int imageIndex = index - 1;

                        // Check if we have a valid image in unified list
                        if (imageIndex <
                            selectedImageEditPreviewBloc
                                .unifiedImageList.length) {
                          var imageItem = selectedImageEditPreviewBloc
                              .unifiedImageList[imageIndex];

                          if (imageItem is ProdImages) {
                            // Existing image
                            ProdImages file = imageItem;
                            if (file.productImage!.isNotEmpty) {
                              return Container(
                                key: ValueKey(file.productimageid),
                                margin: const EdgeInsets.only(right: 8),
                                child: Stack(
                                  alignment: Alignment.topRight,
                                  children: [
                                    GestureDetector(
                                      onTap: () {
                                        // Use the unified image index directly
                                        selectedImageEditPreviewBloc
                                            .onSelectUnifiedImage(imageIndex);
                                      },
                                      child: Container(
                                        width:
                                            MediaQuery.of(context).size.height /
                                                10,
                                        height:
                                            MediaQuery.of(context).size.height /
                                                10,
                                        decoration: BoxDecoration(
                                          border: Border.all(
                                            color: AppColors.appWhite
                                                .withOpacity(0.3),
                                            width: 1,
                                          ),
                                          borderRadius:
                                              BorderRadius.circular(4),
                                        ),
                                        child: ClipRRect(
                                          borderRadius:
                                              BorderRadius.circular(4),
                                          child: extendedImage(
                                              file.productImage.toString(),
                                              context,
                                              200,
                                              200,
                                              cache: true,
                                              fit: BoxFit.cover,
                                              customPlaceHolder:
                                                  AppImages.productPlaceHolder),
                                        ),
                                      ),
                                    ),
                                    Positioned(
                                        top: 2,
                                        right: 2,
                                        child: InkWell(
                                            onTap: () {
                                              setState(() {
                                                selectedImageEditPreviewBloc
                                                    .deleteImage(
                                                        file.productimageid!);
                                              });
                                            },
                                            child: SvgPicture.asset(
                                                AppImages.removeCircle)))
                                  ],
                                ),
                              );
                            }
                          } else if (imageItem is XFile) {
                            // Pending image
                            XFile pendingFile = imageItem;
                            return Container(
                              key: ValueKey('pending_${pendingFile.path}'),
                              margin: const EdgeInsets.only(right: 8),
                              child: Stack(
                                alignment: Alignment.topRight,
                                children: [
                                  GestureDetector(
                                    onTap: () {
                                      // Navigate to the pending image in the unified list
                                      selectedImageEditPreviewBloc
                                          .onSelectUnifiedImage(imageIndex);
                                    },
                                    child: Container(
                                      width:
                                          MediaQuery.of(context).size.height / 10,
                                      height:
                                          MediaQuery.of(context).size.height / 10,
                                      decoration: BoxDecoration(
                                        border: Border.all(
                                          color: Colors.orange.withOpacity(
                                              0.8), // Orange border for pending
                                          width: 2,
                                        ),
                                        borderRadius: BorderRadius.circular(4),
                                      ),
                                      child: ClipRRect(
                                        borderRadius: BorderRadius.circular(4),
                                        child: Image.file(
                                          File(pendingFile.path),
                                          fit: BoxFit.cover,
                                        ),
                                      ),
                                    ),
                                  ),
                                  Positioned(
                                      top: 2,
                                      right: 2,
                                      child: InkWell(
                                          onTap: () {
                                            setState(() {
                                              // Remove from unified list and rebuild
                                              selectedImageEditPreviewBloc
                                                  .unifiedImageList
                                                  .removeAt(imageIndex);
                                              selectedImageEditPreviewBloc
                                                  .separateUnifiedList();
                                              if (selectedImageEditPreviewBloc
                                                  .pendingImages.isEmpty) {
                                                selectedImageEditPreviewBloc
                                                    .hasUnsavedChanges = false;
                                              }
                                            });
                                          },
                                          child: SvgPicture.asset(
                                              AppImages.removeCircle))),
                                  // Pending indicator
                                  Positioned(
                                    bottom: 2,
                                    left: 2,
                                    child: Container(
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 4, vertical: 2),
                                      decoration: BoxDecoration(
                                        color: Colors.orange,
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      child: Text(
                                        'NEW',
                                        style: TextStyle(
                                          color: AppColors.appWhite,
                                          fontSize: 8,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            );
                          }
                        }
                        return Container(key: ValueKey('empty_$imageIndex'));
                      },
                    )
                  : Container(), // Empty container when collapsed
            ),
          );
        });
  }
  //endregion

  Widget imageList() {
    return StreamBuilder<bool>(
        stream: selectedImageEditPreviewBloc.gridViewRefreshCtrl.stream,
        builder: (context, snapshot) {
          return Center(
            child: ReorderableWrap(
              minMainAxisCount: 4,
              // maxMainAxisCount: 5,
              spacing: 4,
              runSpacing: 4,
              padding: EdgeInsets.zero,
              crossAxisAlignment: WrapCrossAlignment.start,
              // crossAxisAlignment: WrapCrossAlignment,
              alignment: WrapAlignment.start,
              runAlignment: WrapAlignment.start,

              ///Reorder
              onReorder: (oldIndex, newIndex) {
                setState(() {
                  final element = selectedImageEditPreviewBloc
                      .productImageResponse!.data!
                      .removeAt(oldIndex);
                  selectedImageEditPreviewBloc.productImageResponse!.data!
                      .insert(newIndex, element);

                  // Mark that reorder changes have been made
                  selectedImageEditPreviewBloc.markReorderChanges();
                });
              },
              header: [
                Consumer<AppConfigDataModel>(
                  builder: (BuildContext context, AppConfigDataModel value,
                      Widget? child) {
                    return InkWell(
                      onTap: () {
                        // Check if image count has already reached the limit of 10
                        if ((selectedImageEditPreviewBloc
                                .productImageResponse!.data!.length) >=
                            (value.appConfig!.productImageLimit)) {
                          return CommonMethods.toastMessage(
                              "${AppStrings.youCantAddMoreThen} ${value.appConfig!.productImageLimit} images",
                              context);
                        }
                        selectedImageEditPreviewBloc
                            .goToSelectEditImageScreen();
                      },
                      child: SizedBox(
                        height: 85,
                        width: 85,
                        child: Container(
                          decoration: BoxDecoration(
                              border: Border.all(
                                  color: AppColors.lightGray2, width: 2),
                              borderRadius:
                                  const BorderRadius.all(Radius.circular(0))),
                          child: const Center(
                            child: Icon(Icons.add),
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ],
              children: selectedImageEditPreviewBloc.productImageResponse!.data!
                  .map((ProdImages file) {
                ///Network Image
                if (file.productImage!.isNotEmpty) {
                  return Container(
                      height: 85,
                      width: 85,
                      decoration: const BoxDecoration(
                        borderRadius: BorderRadius.all(Radius.circular(0)),
                      ),
                      key: ValueKey(file),
                      child: Stack(
                        alignment: Alignment.topRight,
                        children: [
                          InkWell(
                              onTap: () {
                                selectedImageEditPreviewBloc.selectedImageCtrl
                                    .add(file);
                              },
                              child: Center(
                                  child: ClipRRect(
                                      borderRadius: const BorderRadius.all(
                                          Radius.circular(0)),
                                      child: extendedImage(
                                        file.productImage.toString(),
                                        customPlaceHolder:
                                            AppImages.productPlaceHolder,
                                        context,
                                        500,
                                        500,
                                        cache: true,
                                      )

                                      // child: CachedNetworkImage(
                                      //   imageUrl: file.productImage.toString(),
                                      //   //imageUrl: selectedImageEditPreviewBloc.productImageResponse!.data![file].productImage!,
                                      //   fit: BoxFit.fill,
                                      //     height: double.infinity,
                                      //     width: double.infinity,
                                      //   filterQuality: FilterQuality.low,
                                      //   placeholder: (context, url) =>  Center(child: Image.asset(AppImages.noImage,fit: BoxFit.cover,)),
                                      //   errorWidget: (context, url, error) => const Icon(Icons.error),
                                      //
                                      // )
                                      ))),
                          Positioned(
                              top: 2,
                              right: 2,
                              child: InkWell(
                                  onTap: () {
                                    /// Api call to remove
                                    setState(() {
                                      selectedImageEditPreviewBloc
                                          .deleteImage(file.productimageid!);
                                      //selectedImageEditPreviewBloc.selectedImageCtrl.sink.add();
                                    });
                                  },
                                  child:
                                      SvgPicture.asset(AppImages.removeCircle)))
                        ],
                      ));
                }
                return Container();
              }).toList(),
            ),
          );
        });
  }

//endregion
}
