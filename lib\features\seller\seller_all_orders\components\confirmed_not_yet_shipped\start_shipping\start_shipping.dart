import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/seller/seller_all_orders/components/confirmed_not_yet_shipped/start_shipping/delivery_by_logistics/delivery_by_logistics.dart';
import 'package:swadesic/features/seller/seller_all_orders/components/confirmed_not_yet_shipped/start_shipping/delivery_by_seller/delivery_by_seller.dart';
import 'package:swadesic/features/seller/seller_all_orders/components/confirmed_not_yet_shipped/start_shipping/delivery_by_swadesic/delivery_by_swadesic.dart';
import 'package:swadesic/features/seller/seller_all_orders/components/confirmed_not_yet_shipped/start_shipping/start_shipping_bloc.dart';
import 'package:swadesic/features/seller/seller_all_orders/seller_all_orders_common_widget.dart';
import 'package:swadesic/features/seller/seller_all_orders/seller_sub_order/seller_sub_order_bloc.dart';
import 'package:swadesic/model/order_response/get_order_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_test_fields/app_text_fields.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/app_title_and_options/app_title_and_options.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:swadesic/model/order_response/sub_order.dart';
import 'package:swadesic/features/seller/seller_all_orders/seller_all_order_customer_details/seller_all_order_customer_details_bloc.dart';

class StartShipping extends StatefulWidget {
  final List<SubOrder> suborderList;
  final SellerSubOrderBloc sellerSubOrderBloc;
  final Order order;

  const StartShipping({
    Key? key,
    required this.suborderList,
    required this.sellerSubOrderBloc,
    required this.order,
  }) : super(key: key);

  @override
  State<StartShipping> createState() => _StartShippingState();
}

class _StartShippingState extends State<StartShipping> {
  //region Bloc
  late StartShippingBloc startShippingBloc;
  late SellerAllOrderCustomerDetailBloc customerDetailBloc;

  //endregion

  //region Init
  @override
  void initState() {
    super.initState();
    startShippingBloc = StartShippingBloc(
      context: context,
      sellerSubOrderBloc: widget.sellerSubOrderBloc,
      order: widget.order,
      suborderList: widget.suborderList,
    );
    startShippingBloc.init();

    customerDetailBloc =
        SellerAllOrderCustomerDetailBloc(context, widget.order);
    customerDetailBloc.init();
  }

  //endregion

  //region Dispose
  @override
  void dispose() {
    //print("Dispose");
    // TODO: implement dispose
    super.dispose();
  }

  //endregion

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<bool>(
        stream: startShippingBloc.logisticAndLinkCtrl.stream,
        builder: (context, snapshot) {
          return GestureDetector(
              onTap: () {
                CommonMethods.closeKeyboard(context);
              },
              child: Stack(
                children: [
                  SingleChildScrollView(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        selectUnselect(),
                      ],
                    ),
                  ),
                  StreamBuilder<StartShippingState>(
                      stream: startShippingBloc.startShippingCtrl.stream,
                      builder: (context, snapshot) {
                        if (snapshot.data == StartShippingState.Loading) {
                          return const InkWell(
                              child: SizedBox(
                            height: double.infinity,
                            width: double.infinity,
                          ));
                        }
                        return const SizedBox();
                      })
                ],
              ));
        });
  }

  //region Select unselect
  Widget selectUnselect() {
    return Column(
      children: [
        ///Sub title
        Container(
            margin: const EdgeInsets.all(10),
            child: SellerAllOrdersCommonWidgets.sellerBottomSheetSubTitle(
                title: AppStrings.youCanGroup)),

        ///Drop down
        InkWell(
          onTap: () {
            startShippingBloc.selectUnSelect();
          },
          child: Container(
            margin: const EdgeInsets.symmetric(horizontal: 6.5),
            decoration: BoxDecoration(
                color: AppColors.lightestGrey2,
                borderRadius: BorderRadius.circular(7)),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                ///Drop down
                Container(
                  padding: const EdgeInsets.all(10),
                  child: Row(
                    children: [
                      //Grand total
                      Text(
                        AppStrings.selectUnSelect,
                        style: AppTextStyle.heading3Bold(
                            textColor: AppColors.appBlack),
                      ),
                      Expanded(child: horizontalSizedBox(10)),
                      startShippingBloc.isSelectUnselectVisible
                          ? RotatedBox(
                              quarterTurns: 3,
                              child: RotatedBox(
                                  quarterTurns: 4,
                                  child: SvgPicture.asset(AppImages.arrow3)),
                            )
                          : RotatedBox(
                              quarterTurns: 1,
                              child: SvgPicture.asset(AppImages.arrow3))
                    ],
                  ),
                ),

                ///List of product
                Visibility(
                    visible: startShippingBloc.isSelectUnselectVisible,
                    child: subOrderList()),

                ///Selected product count
                Visibility(
                  visible: !startShippingBloc.isSelectUnselectVisible,
                  child: Container(
                      alignment: Alignment.centerLeft,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 20, vertical: 10),
                      child: Text(
                        "${CommonMethods.returnHowManySubOrdersSelected(subOrderList: widget.suborderList) == 0 ? "No" : CommonMethods.returnHowManySubOrdersSelected(subOrderList: widget.suborderList)} ${CommonMethods.returnHowManySubOrdersSelected(subOrderList: widget.suborderList) == 1 ? "suborder" : "suborders"} selected",
                        style: AppTextStyle.settingText(
                            textColor: AppColors.appBlack),
                      )),
                )
              ],
            ),
          ),
        ),

        verticalSizedBox(10),

        Visibility(
          visible: !startShippingBloc.isGrouped,
          child: Padding(
            padding: const EdgeInsets.only(left: 10, right: 10, bottom: 20),
            child: deliveryDate(),
          ),
        ),

        ///Confirm and cancel
        Visibility(
            visible: !startShippingBloc.isShippingDetailVisible,
            child: groupAndCancel()),

        ///Add shipping
        Container(
          padding: const EdgeInsets.all(10),
          margin: const EdgeInsets.all(10),
          child: Text(AppStrings.addShipping,
              maxLines: 2,
              style: AppTextStyle.access1(textColor: AppColors.appBlack)),
        ),

        ///Add shipping and tracking
        Visibility(
            visible: startShippingBloc.isShippingDetailVisible,
            child: addShippingAndTrackingDetail()),

        ///Space
        AppCommonWidgets.bottomListSpace(context: context),
      ],
    );
  }

  //endregion

//region Sub orders list
  Widget subOrderList() {
    return ListView.builder(
        physics: const NeverScrollableScrollPhysics(),
        itemCount: widget.suborderList.length,
        shrinkWrap: true,
        itemBuilder: (buildContext, index) {
          return Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              AppCommonWidgets.subOrderInfo(
                  subOrder: widget.suborderList[index],
                  onTap: () {
                    //Make is select to true
                    startShippingBloc.onSelectSubOrder(
                        subOrder: widget.suborderList[index]);

                    //Date time
                    startShippingBloc.onSelectChangeDate();
                    //If grouped then call
                    if (startShippingBloc.isGrouped) {
                      //Show group button
                      startShippingBloc.isShippingDetailVisible = false;
                      startShippingBloc.logisticAndLinkCtrl.sink.add(true);
                    }
                  },
                  context: context),
              index == widget.suborderList.length - 1
                  ? const SizedBox()
                  : Padding(
                      padding:
                          EdgeInsets.symmetric(vertical: 5, horizontal: 20),
                      child: Divider(
                        color: AppColors.lightGray,
                        height: 1,
                        thickness: 1,
                      ),
                    )
            ],
          );
        });
  }

//endregion

//region Group and cancel
  Widget groupAndCancel() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 10),
      child: Row(
        children: [
          Expanded(
            child: AppCommonWidgets.activeButton(
                buttonName: CommonMethods.returnHowManySubOrdersSelected(
                            subOrderList: widget.suborderList) ==
                        1
                    ? AppStrings.ship
                    : AppStrings.groupToShip,
                onTap: () {
                  startShippingBloc.groupSubOrders(
                      selectedSubOrders:
                          CommonMethods.sellerSelectedSubOrderNumberList(
                              widget.suborderList),
                      subOrderList: widget.suborderList);
                }),
          ),
          horizontalSizedBox(10),
          Expanded(
            child: AppCommonWidgets.inActiveButton(
                buttonName: CommonMethods.isAllOrderSelectedSelected(
                        subOrderList: widget.suborderList)
                    ? AppStrings.cancelAll
                    : AppStrings.cancel,
                onTap: () {
                  startShippingBloc.onTapCancel(
                      subOrderNumbers:
                          CommonMethods.sellerSelectedSubOrderNumberList(
                              widget.suborderList));
                }),
          )
        ],
      ),
    );
  }

//endregion

//region Add shipping and tracking detail
  Widget addShippingAndTrackingDetail() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 10),
      child: StreamBuilder<bool>(
          stream: startShippingBloc.logisticAndLinkCtrl.stream,
          builder: (context, snapshot) {
            return Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                ///Group name
                AppTitleAndOptions(
                  title: AppStrings.groupName,
                  option: AppTextFields.allTextField(
                    context: context,
                    maxEntry: 50,
                    onChanged: () {
                      startShippingBloc.onChangeGroupName();
                    },
                    textEditingController: startShippingBloc.packageNameTextCtr,
                    hintText: AppStrings.pkg,
                  ),
                ),
                verticalSizedBox(20),

                ///Estimate delivery
                deliveryDate(),
                verticalSizedBox(20),

                ///Select delivery method
                selectDeliveryMethod(),
              ],
            );
          }),
    );
  }

//endregion

//region Delivery date
  Widget deliveryDate() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        AppTitleAndOptions(
          title: AppStrings.estimatedDeliveryDate,
          option: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 10),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(
                  startShippingBloc.estimatedDeliveryDate,
                  style:
                      AppTextStyle.settingText(textColor: AppColors.appBlack),
                ),
                horizontalSizedBox(30),
                InkWell(
                  onTap: () {
                    startShippingBloc.onTapCalender();
                  },
                  child: Container(
                    padding: const EdgeInsets.all(10),
                    decoration: BoxDecoration(
                        color: AppColors.textFieldFill1,
                        borderRadius: BorderRadius.all(Radius.circular(10))),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        SvgPicture.asset(
                          AppImages.calender,
                          color: AppColors.appBlack,
                        ),
                        horizontalSizedBox(10),
                        Text(
                          AppStrings.updateDeliveryDate,
                          style: AppTextStyle.access0(
                              textColor: AppColors.appBlack),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

//endregion

//region Select product delivery method
  Widget selectDeliveryMethod() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        //Select product delivery method

        AppTitleAndOptions(
          title: AppStrings.selectProductDeliveryMethod,
          option: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ///Self
              appRadioCheckBox(
                  isActive: startShippingBloc.isSelfDelivery,
                  text: "Self-delivery by store",
                  isExpand: false,
                  onTap: () {
                    startShippingBloc.onSelectDeliveryMethod(true);
                  }),

              ///Delivery partner
              appRadioCheckBox(
                  isActive: !startShippingBloc.isSelfDelivery &&
                      !startShippingBloc.isSwadesicShipping,
                  text: "Delivery by logistics partner",
                  isExpand: false,
                  onTap: () {
                    startShippingBloc.onSelectDeliveryMethod(false);
                    startShippingBloc.setSwadesicShipping(false);
                  }),

              ///Swadesic Shipping
              appRadioCheckBox(
                  isActive: false,
                  // isActive: startShippingBloc.isSwadesicShipping,
                  text: "Swadesic shipping (Coming soon)",
                  textColor: AppColors.disableBlack,
                  isExpand: false,
                  onTap: () {
                    // startShippingBloc.onSelectDeliveryMethod(false);
                    // startShippingBloc.setSwadesicShipping(true);
                  }),
            ],
          ),
        ),
        verticalSizedBox(20),

        ///Self delivery
        Visibility(
            visible: startShippingBloc.isSelfDelivery, child: selfDelivery()),

        ///Delivery by logistic
        Visibility(
            visible: !startShippingBloc.isSelfDelivery &&
                !startShippingBloc.isSwadesicShipping,
            child: deliveryByLogistic()),

        ///Swadesic shipping section
        Visibility(
            visible: startShippingBloc.isSwadesicShipping,
            child: deliveryBySwadesic()),

        verticalSizedBox(20),

        ///Mark as shipped
        Visibility(
            visible: startShippingBloc.isShippingDetailVisible,
            child: markAsShipped())
      ],
    );
  }

//endregion

//region Self delivery
  Widget selfDelivery() {
    return DeliveryBySeller(
      startShippingBloc: startShippingBloc,
    );
  }

//endregion

  //region Delivery by logistic
  Widget deliveryByLogistic() {
    return DeliveryByLogistics(
      startShippingBloc: startShippingBloc,
    );
  }

  //endregion

  //region Delivery by swadesic
  Widget deliveryBySwadesic() {
    return DeliveryBySwadesic(
      startShippingBloc: startShippingBloc,
      customerDetailBloc: customerDetailBloc,
    );
  }
  //endregion

//region Market as shipped button
  Widget markAsShipped() {
    return Column(
      children: [
        ///Additional notes

        AppTitleAndOptions(
          title: AppStrings.additionalNotes,
          option: AppTextFields.allTextField(
            maxEntry: 500,
            maxLines: 5,
            minLines: 5,
            context: context,
            textEditingController: startShippingBloc.notesTextCtrl,
            hintText: AppStrings.note,
          ),
        ),
        verticalSizedBox(20),

        Text(
          "Marking shipped will update your customer that you shipped the listed products",
          style: AppTextStyle.subTitle(textColor: AppColors.writingBlack1),
        ),

        verticalSizedBox(10),

        Row(
          children: [
            Expanded(
              child: StreamBuilder<StartShippingState>(
                  stream: startShippingBloc.markAsShippingCtrl.stream,
                  initialData: StartShippingState.Success,
                  builder: (context, snapshot) {
                    bool isLoading =
                        snapshot.data == StartShippingState.Loading;
                    Color buttonColor = startShippingBloc.isGroupNameUnique
                        ? AppColors.brandBlack
                        : AppColors.tertiaryGreen;

                    return CupertinoButton(
                      onPressed: isLoading
                          ? null
                          : () {
                              startShippingBloc.changeTheStatus();
                            },
                      padding: EdgeInsets.zero,
                      child: Container(
                        width: double.infinity,
                        padding: const EdgeInsets.symmetric(
                            vertical: 13, horizontal: 10),
                        decoration: BoxDecoration(
                          color: isLoading
                              ? buttonColor.withOpacity(0.7)
                              : buttonColor,
                          borderRadius: BorderRadius.circular(100),
                        ),
                        child: SizedBox(
                          height: 20, // Fixed height to prevent size changes
                          child: Center(
                            child: isLoading
                                ? SizedBox(
                                    height: 20,
                                    width: 20,
                                    child: SpinKitFadingCircle(
                                      color: AppColors.appWhite,
                                      size: 20,
                                    ),
                                  )
                                : Text(
                                    AppStrings.markAsShipped,
                                    style: AppTextStyle.access0(
                                        textColor: AppColors.appWhite),
                                  ),
                          ),
                        ),
                      ),
                    );
                  }),
            ),
          ],
        ),
      ],
    );
  }
//endregion
}
