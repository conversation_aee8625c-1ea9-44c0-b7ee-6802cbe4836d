import 'package:expandable/expandable.dart';
import 'package:flutter/material.dart';
import 'package:swadesic/features/faq/faq_navigation.dart';
import 'package:swadesic/features/faq/faq_screen_bloc.dart';
import 'package:swadesic/features/share_with_image/share_with_image_screen.dart';
import 'package:swadesic/features/widgets/custom_image_container/custom_image_container.dart';
import 'package:swadesic/model/faq/faq_model.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_enums.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

class FaqScreen extends StatefulWidget {
  final String? initialCategoryId;
  final String? initialQuestionId;

  const FaqScreen({
    Key? key,
    this.initialCategoryId,
    this.initialQuestionId,
  }) : super(key: key);

  @override
  State<FaqScreen> createState() => _FaqScreenState();
}

class _FaqScreenState extends State<FaqScreen>
    with SingleTickerProviderStateMixin {
  late FaqScreenBloc faqScreenBloc;
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    faqScreenBloc = FaqScreenBloc(
      context: context,
      initialCategoryId: widget.initialCategoryId,
      initialQuestionId: widget.initialQuestionId,
    );
  }

  @override
  void dispose() {
    faqScreenBloc.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.appWhite,
      appBar: AppBar(
        backgroundColor: AppColors.appWhite,
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: AppColors.appBlack),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: Text(
          'Know about Swadesic',
          style: AppTextStyle.pageHeading(textColor: AppColors.appBlack),
        ),
        actions: [
          IconButton(
            icon: Icon(Icons.share, color: AppColors.appBlack),
            onPressed: () => _shareCurrentCategory(),
          ),
        ],
      ),
      body: StreamBuilder<FaqScreenState>(
        stream: faqScreenBloc.stateStream,
        initialData: FaqScreenState.Initial,
        builder: (context, snapshot) {
          if (snapshot.data == FaqScreenState.Loading) {
            return Center(
              child: CircularProgressIndicator(
                color: AppColors.brandBlack,
              ),
            );
          }

          if (snapshot.data == FaqScreenState.Failed) {
            return AppCommonWidgets.errorWidget(
              errorMessage: 'Failed to load FAQ data',
              onTap: () {
                faqScreenBloc.init();
              },
            );
          }

          if (snapshot.data == FaqScreenState.Success) {
            // Scroll to specific question if deep linking
            WidgetsBinding.instance.addPostFrameCallback((_) {
              _scrollToQuestionIfNeeded();
            });

            return Column(
              children: [
                _buildCategoryTabs(),
                Expanded(
                  child: _buildFaqList(),
                ),
              ],
            );
          }

          return const SizedBox();
        },
      ),
    );
  }

  Widget _buildCategoryTabs() {
    // Check if categories are loaded
    if (faqScreenBloc.faqDataModel.getFaqCategories.isEmpty) {
      return const SizedBox();
    }

    return Container(
      height: 40, // Reduced from 50 to 40
      margin:
          const EdgeInsets.symmetric(vertical: 8), // Reduced vertical margin
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: faqScreenBloc.faqDataModel.getFaqCategories.length,
        padding: const EdgeInsets.symmetric(horizontal: 10),
        itemBuilder: (context, index) {
          final isSelected = index == faqScreenBloc.selectedCategoryIndex;
          return GestureDetector(
            onTap: () => faqScreenBloc.changeCategory(index),
            child: Container(
              margin: const EdgeInsets.only(right: 8),
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 6),
              height: 32, // Explicitly set a lower height
              decoration: BoxDecoration(
                color: isSelected ? AppColors.appBlack : AppColors.appWhite,
                borderRadius: BorderRadius.circular(20),
                border: Border.all(
                  color: AppColors.appBlack,
                  width: 1,
                ),
              ),
              alignment: Alignment.center,
              child: Text(
                faqScreenBloc.faqDataModel.getFaqCategories[index].name,
                style: AppTextStyle.access0(
                  textColor:
                      isSelected ? AppColors.appWhite : AppColors.appBlack,
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildFaqList() {
    // Check if categories are loaded
    if (faqScreenBloc.faqDataModel.getFaqCategories.isEmpty ||
        faqScreenBloc.selectedCategoryIndex >=
            faqScreenBloc.faqDataModel.getFaqCategories.length) {
      return const Center(
        child: Text('No FAQ data available'),
      );
    }

    final currentCategory = faqScreenBloc
        .faqDataModel.getFaqCategories[faqScreenBloc.selectedCategoryIndex];

    return ListView.separated(
      controller: _scrollController,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: currentCategory.items.length,
      separatorBuilder: (context, index) => const Divider(height: 1),
      itemBuilder: (context, index) {
        final faqItem = currentCategory.items[index];
        return _buildFaqItem(faqItem, index);
      },
    );
  }

  Widget _buildFaqItem(FaqItem faqItem, int index) {
    final currentCategory = faqScreenBloc
        .faqDataModel.getFaqCategories[faqScreenBloc.selectedCategoryIndex];

    return Container(
      key: ValueKey('faq_item_${faqItem.id}'), // Add key for scrolling
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: ExpandablePanel(
        theme: ExpandableThemeData(
          animationDuration: Duration(milliseconds: 300),
          iconPlacement: ExpandablePanelIconPlacement.right,
          tapBodyToCollapse: true,
          tapHeaderToExpand: true,
          tapBodyToExpand: true,
          iconPadding: EdgeInsets.zero,
          headerAlignment: ExpandablePanelHeaderAlignment.center,
          iconSize: 24,
          iconColor: AppColors.appBlack,
        ),
        header: Padding(
          padding: const EdgeInsets.symmetric(vertical: 12),
          child: Row(
            children: [
              Expanded(
                child: Text(
                  faqItem.question,
                  style: AppTextStyle.access0(
                    textColor: AppColors.appBlack,
                  ),
                ),
              ),
              IconButton(
                icon: Icon(
                  Icons.share,
                  size: 20,
                  color: AppColors.appBlack,
                ),
                onPressed: () => _shareQuestion(currentCategory.id, faqItem),
                padding: const EdgeInsets.all(4),
                constraints: const BoxConstraints(
                  minWidth: 32,
                  minHeight: 32,
                ),
              ),
            ],
          ),
        ),
        collapsed: const SizedBox(),
        expanded: Padding(
          padding: const EdgeInsets.only(bottom: 16, top: 8),
          child: Text(
            faqItem.answer,
            style: AppTextStyle.contentText0(
              textColor: AppColors.writingBlack2,
            ),
          ),
        ),
        controller: ExpandableController(
          initialExpanded: faqItem.isExpanded,
        ),
      ),
    );
  }

  // Share current category
  void _shareCurrentCategory() {
    if (faqScreenBloc.faqDataModel.getFaqCategories.isEmpty ||
        faqScreenBloc.selectedCategoryIndex >=
            faqScreenBloc.faqDataModel.getFaqCategories.length) {
      return;
    }

    final currentCategory = faqScreenBloc
        .faqDataModel.getFaqCategories[faqScreenBloc.selectedCategoryIndex];

    final categoryUrl = FaqNavigation.generateCategoryLink(currentCategory.id);

    CommonMethods.accessBottomSheet(
      screen: ShareWithImageScreen(
        url: categoryUrl,
        imageLink: null,
        imageType: CustomImageContainerType.user,
        entityType: EntityType.USER,
        message: "Check out these ${currentCategory.name} FAQs on Swadesic!",
      ),
      context: context,
    );
  }

  // Share specific question
  void _shareQuestion(String categoryId, FaqItem faqItem) {
    final questionUrl =
        FaqNavigation.generateQuestionLink(categoryId, faqItem.id);

    CommonMethods.accessBottomSheet(
      screen: ShareWithImageScreen(
        url: questionUrl,
        imageLink: null,
        imageType: CustomImageContainerType.user,
        entityType: EntityType.USER,
        message: "Found this helpful FAQ: \"${faqItem.question}\" on Swadesic!",
      ),
      context: context,
    );
  }

  // Scroll to specific question if deep linking
  void _scrollToQuestionIfNeeded() {
    if (widget.initialQuestionId != null && widget.initialCategoryId != null) {
      final questionIndex = faqScreenBloc.faqDataModel.getQuestionIndexById(
        widget.initialCategoryId!,
        widget.initialQuestionId!,
      );

      if (questionIndex >= 0) {
        // Calculate approximate position (each item is roughly 80px)
        final position = questionIndex * 80.0;

        // Scroll to the question with animation
        _scrollController.animateTo(
          position,
          duration: const Duration(milliseconds: 500),
          curve: Curves.easeInOut,
        );
      }
    }
  }
}
