import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/data_model/post_data_model/post_data_model.dart';
import 'package:swadesic/features/post/single_post_view/single_post_bloc.dart';
import 'package:swadesic/features/post/sub_commnet/sub_comment_bloc.dart';
import 'package:swadesic/features/widgets/post_widgets/comment_card.dart';
import 'package:swadesic/features/widgets/post_widgets/sub_comment_card.dart';
import 'package:swadesic/model/post_response/get_all_post_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_widgets.dart';

class SubComments extends StatefulWidget {
  final PostDetail commentDetail;
  final SinglePostViewBloc singlePostViewBloc;
  final bool isParentPost;

  const SubComments({
    super.key, 
    required this.commentDetail, 
    required this.singlePostViewBloc,
    this.isParentPost = false,
  });

  @override
  State<SubComments> createState() => _SubSubCommentState();
}

class _SubSubCommentState extends State<SubComments>  with AutomaticKeepAliveClientMixin<SubComments>{

  //Keep alive
  @override
  bool get wantKeepAlive => true;

  //region Bloc
  late SubCommentBloc subCommentBloc;

  //endregion

  //region Init
  @override
  void initState() {
    subCommentBloc = SubCommentBloc(context, widget.commentDetail,widget.singlePostViewBloc);
    super.initState();
  }
  //endregion

  @override
  Widget build(BuildContext context) {
    return body();
  }

  //region Body
  Widget body() {
    return Column(
      children: [
        newlyAddedReplies(),
        subCommentList(),
      ],
    );
  }

//endregion

//region Sub comment list
  Widget subCommentList() {
    var postDataModel = Provider.of<PostDataModel>(context, listen: false);

    List<PostDetail> subCommentList = [];
    subCommentList = postDataModel.allPostDetailList.where((element) {
      final parentMatch = element.parentCommentId == widget.commentDetail.postOrCommentReference;
      final subCommentMatch = subCommentBloc.subCommentList.any((subComment) => subComment.postOrCommentReference == element.postOrCommentReference);
      return parentMatch && subCommentMatch;
    }).toList();

    return StreamBuilder<SubCommentState>(
      stream: subCommentBloc.subCommentStateController.stream,
      initialData:SubCommentState.ViewReplies ,
      builder: (context, snapshot) {
        //View Replies - Only show if not a parent post
        if(snapshot.data == SubCommentState.ViewReplies && !widget.isParentPost){
          return InkWell(
            onTap: () {
              subCommentBloc.getCommentList(isPaginationLoading: false);
            },
            child: Container(
              width: double.infinity,
                alignment: Alignment.centerLeft,
                padding: const EdgeInsets.only(right: 10,top: 10),
                child: Text(
                  "view more replies",
                  style: AppTextStyle.access0(textColor: AppColors.writingBlack1),
                )),
          );
        }
        //Loading
        if(snapshot.data == SubCommentState.Loading){
          return AppCommonWidgets.appCircularProgress(isPaginationProgress: true);
        }
        //Success
        if(snapshot.data == SubCommentState.Success){
          return Consumer<PostDataModel>(
            builder: (BuildContext context, PostDataModel data, Widget? child) {
              List<PostDetail> subCommentList = [];
              // subCommentList = data.allPostDetailList.where((element) => element.parentCommentId == widget.commentDetail.postOrCommentReference).toList();
              subCommentList = data.allPostDetailList.where((element) {
                final parentMatch = element.parentCommentId == widget.commentDetail.postOrCommentReference;
                final subCommentMatch = subCommentBloc.subCommentList.any((subComment) => subComment.postOrCommentReference == element.postOrCommentReference);
                return parentMatch && subCommentMatch;
              }).toList();

              //print("Updated sub comment leangth is ${subCommentList.length} ");
              subCommentList.sort((a, b) => a.createdDate!.compareTo(b.createdDate!));
              //print("Total sub comment list is ${subCommentList.length}");
              //Success
              return Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  ListView.builder(
                      itemCount: subCommentList.length,
                      // controller: subCommentBloc.scrollController,
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemBuilder: (context, index) {
                        return SubCommentCard(
                          postDetail: subCommentList[index],
                          onTapDelete: () {
                            subCommentBloc.confirmDelete(postDetail: subCommentList[index]);
                          },
                          onTapDrawer: () {
                            subCommentBloc.onTapDrawer(postDetail: subCommentList[index]);
                          },
                          onTapEdit: () {
                            subCommentBloc.goToEditPost(postDetail: subCommentList[index]);
                          },
                          onTapHeart: () {
                            subCommentBloc.onTapHeart(postDetail: subCommentList[index]);
                          },
                          onTapReport: () {},
                          onTapShare: () {
                            subCommentBloc.onTapShare(postDetail: subCommentList[index]);
                          },
                          onTapProfileImage: () {
                            subCommentBloc.onTapUserOrStoreIcon(reference: subCommentList[index].createdBy!.userOrStoreReference!);
                          },
                          onTapPost: () {
                            subCommentBloc.goToSinglePostView(postReference: subCommentList[index].postOrCommentReference!);
                          }, onTapReply: (){
                          //Take out child comment reference also take out handle
                          widget.singlePostViewBloc.replyCommentOrPostDetail =
                          {
                            "reference": subCommentList[index].postOrCommentReference,
                            "handle":subCommentList[index].createdBy!.handle
                          };
                          // Show comment field and trigger reply
                          widget.singlePostViewBloc.commentFieldVisibilityCtrl.sink.add(true);
                          widget.singlePostViewBloc.commentFieldsBloc.onTapReply(replyCommentOrPostDetail:widget.singlePostViewBloc.replyCommentOrPostDetail);

                          // singlePostViewBloc.onTapReply(childComment: rootCommentList[index]);
                        },
                        );
                      }),
                  //After heating view replies show a progress
                  Visibility(
                      visible: subCommentList.length != widget.commentDetail.commentCount!,
                      child: viewMoreLoading(subCommentList: subCommentList))
                ],
              );

            },
          );
        }
        //Failed
        return AppCommonWidgets.errorWidget(onTap: (){});

      }
    );
  }
//endregion


  //region Newly added replies
  Widget newlyAddedReplies(){
      return Consumer<PostDataModel>(
      builder: (BuildContext context, PostDataModel data, Widget? child) {
        List<PostDetail> newReplies = [];
        newReplies = data.recentlyAddedPostDetail.where((element) => element.parentCommentId == widget.commentDetail.postOrCommentReference).toList();

        //print("Updated sub comment leangth is ${newReplies.length} ");
        newReplies.sort((a, b) => a.createdDate!.compareTo(b.createdDate!));
        //print("Total sub comment list is ${newReplies.length}");
        //Success
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListView.builder(
                itemCount: newReplies.length,
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemBuilder: (context, index) {
                  return SubCommentCard(
                    postDetail: newReplies[index],
                    onTapDelete: () {
                      subCommentBloc.confirmDelete(postDetail: newReplies[index]);
                    },
                    onTapDrawer: () {
                      subCommentBloc.onTapDrawer(postDetail: newReplies[index]);
                    },
                    onTapEdit: () {
                      subCommentBloc.goToEditPost(postDetail: newReplies[index]);
                    },
                    onTapHeart: () {
                      subCommentBloc.onTapHeart(postDetail: newReplies[index]);
                    },
                    onTapReport: () {},
                    onTapShare: () {
                      subCommentBloc.onTapShare(postDetail: newReplies[index]);
                    },
                    onTapProfileImage: () {
                      subCommentBloc.onTapUserOrStoreIcon(reference: newReplies[index].createdBy!.userOrStoreReference!);
                    },
                    onTapPost: () {
                      subCommentBloc.goToSinglePostView(postReference: newReplies[index].postOrCommentReference!);
                    }, onTapReply: (){
                    //Take out child comment reference also take out handle
                    widget.singlePostViewBloc.replyCommentOrPostDetail =
                    {
                      "reference": newReplies[index].postOrCommentReference,
                      "handle":newReplies[index].createdBy!.handle
                    };
                    // Show comment field and trigger reply
                    widget.singlePostViewBloc.commentFieldVisibilityCtrl.sink.add(true);
                    widget.singlePostViewBloc.commentFieldsBloc.onTapReply(replyCommentOrPostDetail:widget.singlePostViewBloc.replyCommentOrPostDetail);

                    // singlePostViewBloc.onTapReply(childComment: rootCommentList[index]);
                  },
                  );
                }),
          ],
        );

      },
    );
  }
  //endregion

//region View more Loading
Widget viewMoreLoading({required List<PostDetail> subCommentList }){
  var postDataModel = Provider.of<PostDataModel>(context, listen: false);
  List<PostDetail> newReplies = [];
  newReplies = postDataModel.recentlyAddedPostDetail.where((element) => element.parentCommentId == widget.commentDetail.postOrCommentReference).toList();

  return StreamBuilder<SubCommentState>(
      stream: subCommentBloc.viewMoreStateController.stream,
      builder: (context, snapshot) {
        //Loading
        if(snapshot.data == SubCommentState.PaginationLoading){
          return AppCommonWidgets.appCircularProgress(isPaginationProgress: true);
        }
        //Empty
        if(snapshot.data == SubCommentState.Empty){
          return const SizedBox();
        }

        return subCommentList.length != newReplies.length
            ? InkWell(
          onTap: () {
            //Increase the offset value. offset = (offset + limit)
            subCommentBloc.offset = subCommentBloc.limit + subCommentBloc.offset;
            subCommentBloc.getCommentList(isPaginationLoading: true);
          },
          child: Container(
              alignment: Alignment.centerLeft,
              padding: const EdgeInsets.only(left: 16, right: 10,top: 10),
              child: Text(
                "view more replies",
                style: AppTextStyle.access0(textColor: AppColors.writingBlack1),
              )),
        )
            : const SizedBox();
      }
    );
}
//endregion
}
