import 'package:expandable/expandable.dart';
import 'package:flutter/material.dart';
import 'package:swadesic/features/seller/seller_all_orders/components/auto_cancelled/seller_cancelled_before_confirm_bloc.dart';
import 'package:swadesic/features/seller/seller_all_orders/seller_all_orders_common_widget.dart';
import 'package:swadesic/features/seller/seller_all_orders/seller_sub_order/seller_sub_order_bloc.dart';
import 'package:swadesic/model/order_response/get_order_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:swadesic/model/order_response/sub_order.dart';

class AutoCancelled extends StatefulWidget {
  final List<SubOrder> suborderList;
  final Order order;
  final SellerSubOrderBloc sellerSubOrderBloc;

  const AutoCancelled({
    Key? key,
    required this.suborderList,
    required this.order,
    required this.sellerSubOrderBloc,
  }) : super(key: key);

  @override
  State<AutoCancelled> createState() => _AutoCancelledState();
}

class _AutoCancelledState extends State<AutoCancelled> {
  // region Bloc
  late AutoCancelledBloc autoCancelledBloc;

  // endregion

  // region Init
  @override
  void initState() {
    autoCancelledBloc = AutoCancelledBloc(context, widget.suborderList, widget.order, widget.sellerSubOrderBloc);
    autoCancelledBloc.init();
    super.initState();
  }
  // endregion


  //region Dis update
  @override
  void didUpdateWidget(covariant AutoCancelled oldWidget) {
    autoCancelledBloc = AutoCancelledBloc(context, widget.suborderList, widget.order, widget.sellerSubOrderBloc);
    autoCancelledBloc.init();
    super.didUpdateWidget(oldWidget);
  }
  //endregion






  @override
  Widget build(BuildContext context) {
    // late ExpandableController expandableController = ExpandableController();
    // expandableController.notifyListeners()
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 30, horizontal: 10),
      decoration: BoxDecoration(
          color: AppColors.appWhite,
          border: Border(
              bottom: BorderSide(
                  color: AppColors.lightStroke
              )
          )
      ),
      child: ExpandablePanel(
        //region Theme
        theme: ExpandableThemeData(
          animationDuration: Duration(microseconds: 100),
          iconPlacement: ExpandablePanelIconPlacement.right,
          // alignment: Alignment.bottomRight
          tapBodyToCollapse: true,
          tapHeaderToExpand: true,
          tapBodyToExpand: true,
          iconPadding: EdgeInsets.zero,
          //useInkWell: false,
          // iconRotationAngle:math.pi/2,
          headerAlignment: ExpandablePanelHeaderAlignment.center,

          iconSize: 40,
          iconColor: AppColors.appBlack,

          // iconPadding: EdgeInsets.symmetric( horizontal: 10),
          //iconColor: Colors.green
        ),
        //endregion

        //Waiting for confirmation
        //region Header
        header: header(),
        //endregion
        collapsed: needHelpAndHowRefundCalculate(),

        expanded: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            needHelpAndHowRefundCalculate(),

            Container(
              padding: const EdgeInsets.only(top: 30),
              color: AppColors.appWhite,
              child: ListView.builder(
                  physics: const NeverScrollableScrollPhysics(),
                  shrinkWrap: true,
                  itemCount: autoCancelledBloc.suborderList.length,
                  itemBuilder: (context, index) {
                    return Column(
                      children: [
                        Container(
                          padding: const EdgeInsets.symmetric(vertical: 15),
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              productInfoCard(context: context, subOrder: autoCancelledBloc.suborderList[index],
                              isCancelledOnVisible: true
                              ),
                              //Reason
                              Text("${AppStrings.reason}: ${autoCancelledBloc.suborderList[index].cancellationReason??AppStrings.autoCancelledAfter36Hour}",
                              textAlign: TextAlign.left,
                              style: AppTextStyle.heading3Regular(textColor: AppColors.appBlack,),
                              )

                            ],
                          ),
                        ),
                        //Divider
                        Visibility(
                          visible: autoCancelledBloc.suborderList.length-1 != index,
                          child: Container(
                            color: AppColors.appWhite,
                            child: Padding(
                              padding: const EdgeInsets.symmetric(vertical: 10),
                              child: divider(),
                            ),
                          ),
                        ),
                      ],
                    );
                  }),
            ),
          ],
        ),
      ),
    );
  }

  //region Header
  Widget header() {
    return SellerAllOrdersCommonWidgets.sellerCommonComponent(
      icon: AppImages.packageIcon,
      componentName: AppStrings.autoCancelled,
      suborderList: autoCancelledBloc.suborderList,
      isEstimateDeliveryShow: false,
      additionalWidgets:
      Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            alignment: Alignment.centerLeft,
            child: Text(AppStrings.belowProductsGotAutomaticallyCancelled,style: AppTextStyle.normalTextBold(textColor: AppColors.appBlack),),
          ),
        ],
      ),

    );

    // return Column(
    //   mainAxisSize: MainAxisSize.min,
    //   mainAxisAlignment: MainAxisAlignment.center,
    //   crossAxisAlignment: CrossAxisAlignment.center,
    //   children: [
    //     //Icon and status
    //     Container(
    //       alignment: Alignment.centerLeft,
    //       height: 25,
    //       child: Row(
    //         mainAxisSize: MainAxisSize.min,
    //         mainAxisAlignment: MainAxisAlignment.start,
    //         crossAxisAlignment: CrossAxisAlignment.center,
    //         children: [
    //           SvgPicture.asset(AppImages.loading),
    //           horizontalSizedBox(10),
    //           Text(AppStrings.waitingForConfirmation,style: AppTextStyle.normalTextBold(textColor: AppColors.appBlack),),
    //         ],
    //       ),
    //     ),
    //     verticalSizedBox(15),
    //     //Sub order,items
    //     Container(
    //       height: 20,
    //       alignment: Alignment.centerLeft,
    //       child: Row(
    //         mainAxisSize: MainAxisSize.min,
    //         mainAxisAlignment: MainAxisAlignment.start,
    //         crossAxisAlignment: CrossAxisAlignment.center,
    //         children: [
    //           Text(CommonMethods.calculateItemsInSuborderList(subOrderList: waitingForConfirmationBloc.suborderList),
    //           style: AppTextStyle.normalTextSemiBold(textColor: AppColors.appBlack),
    //           ),
    //           horizontalSizedBox(10),
    //           Text(CommonMethods.calculateSubOrdersInSuborderList(subOrderList: widget.suborderList),
    //             style: AppTextStyle.normalTextSemiBold(textColor: AppColors.appBlack),
    //           ),
    //         ],
    //       ),
    //     ),
    //     verticalSizedBox(5),
    //     //Estimate delivery
    //     Container(
    //       height: 20,
    //       alignment: Alignment.centerLeft,
    //       child: Text("${AppStrings.deliveryEstimate} ${waitingForConfirmationBloc.suborderList.first.estimatedDeliveryDate}",
    //       style: AppTextStyle.heading2SemiBold(textColor: AppColors.writingColor2,fontSize: 16),
    //       ),
    //     ),
    //     verticalSizedBox(20),
    //     //Timer and Message
    //     Container(
    //       height: 20,
    //       alignment: Alignment.centerLeft,
    //       child: Row(
    //         mainAxisSize: MainAxisSize.min,
    //         mainAxisAlignment: MainAxisAlignment.start,
    //         crossAxisAlignment: CrossAxisAlignment.center,
    //         children: [
    //           Text("35:54 mins",
    //             style: AppTextStyle.heading1Bold(textColor: AppColors.writingColor2),
    //           ),
    //           horizontalSizedBox(10),
    //           Text(AppStrings.autoCancelledIfNotConfirmed,
    //             style: AppTextStyle.normalTextBold(textColor: AppColors.orange),
    //           ),
    //         ],
    //       ),
    //     ),
    //
    //     verticalSizedBox(10),
    //
    //
    //
    //   ],
    // );
  }

  //endregion


  //region Need help and how refund calculate
  Widget needHelpAndHowRefundCalculate() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        //     Row(
        //       mainAxisSize: MainAxisSize.min,
        //       mainAxisAlignment: MainAxisAlignment.start,
        //       crossAxisAlignment: CrossAxisAlignment.center,
        //       children: [
        //         Expanded(
        //             child: AppCommonWidgets.inActiveButton(
        //                 buttonName: AppStrings.needHelp,
        //                 onTap: () {
        //                   CommonMethods.reportAndSuggestion(context: context);
        //
        //                 })),
        //       ],
        //     ),
        // verticalSizedBox(20),
        InkWell(
            onTap: (){
              // cancelledByYouBeforeShippingBloc.onTapHowRefundCalculated();
            },
            child: SellerAllOrdersCommonWidgets.howRefundAmountCalculated(subOrderList: autoCancelledBloc.suborderList, sellerSubOrderBloc: autoCancelledBloc.sellerSubOrderBloc))
      ],
    );
  }
//endregion
}
