import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/seller/seller_all_orders/components/initiate_return/InitiateReturnBottomSheet/by_logistics/delivery_by_logistics.dart';
import 'package:swadesic/features/seller/seller_all_orders/components/initiate_return/InitiateReturnBottomSheet/by_seller/delivery_by_seller.dart';
import 'package:swadesic/features/seller/seller_all_orders/components/initiate_return/InitiateReturnBottomSheet/initiate_return_bottom_sheet_bloc.dart';
import 'package:swadesic/features/seller/seller_all_orders/seller_all_orders_bloc.dart';
import 'package:swadesic/features/seller/seller_all_orders/seller_all_orders_common_widget.dart';
import 'package:swadesic/model/order_response/get_order_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_drop_down/app_drop_down.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:swadesic/model/order_response/sub_order.dart';


class InitiateReturnBottomSheetScreen extends StatefulWidget {
  final List<SubOrder> suborderList;
  final SellerAllOrdersBloc sellerAllOrdersBloc;
  final Order order;

  const InitiateReturnBottomSheetScreen({Key? key, required this.suborderList, required this.sellerAllOrdersBloc, required this.order}) : super(key: key);

  @override
  State<InitiateReturnBottomSheetScreen> createState() => _InitiateReturnBottomSheetScreenState();
}

class _InitiateReturnBottomSheetScreenState extends State<InitiateReturnBottomSheetScreen> {
  //region Bloc
  late InitiateReturnBottomSheetBloc initiateReturnBottomSheetBloc;

  //endregion

  //region Init
  @override
  void initState() {
    initiateReturnBottomSheetBloc = InitiateReturnBottomSheetBloc(context,widget.sellerAllOrdersBloc,widget.order);
    initiateReturnBottomSheetBloc.init();
    // TODO: implement initState
    super.initState();
  }

  //endregion

  //region Dispose
  @override
  void dispose() {
    //print("Dispose");
    // TODO: implement dispose
    super.dispose();
  }

  //endregion

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
        onTap: () {
          CommonMethods.closeKeyboard(context);
        },
        child: Stack(
          children: [
            SingleChildScrollView(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  verticalSizedBox(20),
                  selectUnselect(),
                ],
              ),
            ),
            StreamBuilder<StartShippingState>(
              stream: initiateReturnBottomSheetBloc.startShippingCtrl.stream,
              builder: (context, snapshot) {

                if(snapshot.data == StartShippingState.Loading){
                  return const InkWell(child: SizedBox(height: double.infinity,width: double.infinity,));
                }
                return const SizedBox();


              }
            )

          ],
        ));
  }

  //region Select unselect
  Widget selectUnselect() {
    return StreamBuilder<bool>(
        stream: initiateReturnBottomSheetBloc.bottomSheetRefreshCtrl.stream,
        builder: (context, snapshot) {
          return Column(
            children: [
              Container(
                margin: const EdgeInsets.only(left: 10, right: 10, bottom: 10),
                child: Row(
                  children: [
                    Expanded(
                        child: appText(AppStrings.youCanGroup,
                            fontSize: 16,
                            fontWeight: FontWeight.w400,
                            color: AppColors.writingColor2,
                            maxLine: 3,
                            fontFamily: AppConstants.rRegular)),
                  ],
                ),
              ),

              ///Drop down
              AppDropDown(dropDownName:AppStrings.selectUnSelect,dropDownWidget:subOrderList() ),

              ///Confirm and cancel
              Visibility(
                  visible: !initiateReturnBottomSheetBloc.isShippingDetailVisible,
                  child: group()),

              ///Add shipping and tracking
              Visibility(
                  visible: initiateReturnBottomSheetBloc.isShippingDetailVisible,
                  child: addShippingAndTrackingDetail())
            ],
          );
        });
  }

  //endregion

//region Sub orders list
  Widget subOrderList() {
    return Container(
      margin: const EdgeInsets.all(5),
      decoration: BoxDecoration(
          border: Border.all(
            color: AppColors.lightGray2,
            width: 1,
          ),
          borderRadius: const BorderRadius.all(Radius.circular(10))),
      child: ListView.builder(
          physics: const NeverScrollableScrollPhysics(),
          itemCount: widget.suborderList.length,
          shrinkWrap: true,
          itemBuilder: (buildContext, index) {
            return Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                orderDetail(index),
                index == widget.suborderList.length - 1 ? const SizedBox() : divider()
              ],
            );
          }),
    );
  }

//endregion

//region Order Detail
  Widget orderDetail(int index) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 25),
      child: InkWell(
        onTap: () {
          //Make is select to true
          initiateReturnBottomSheetBloc.onSelectSubOrder(
              subOrder: widget.suborderList[index]);

          //If grouped then call
          if (initiateReturnBottomSheetBloc.isGrouped) {
            //Show group button
            initiateReturnBottomSheetBloc.isShippingDetailVisible = false;
            initiateReturnBottomSheetBloc.bottomSheetRefreshCtrl.sink.add(true);
            //If no product is selected then call delete sub order from group
            List<String> data =
                CommonMethods.sellerSelectedSubOrderNumberList(widget.suborderList);
            if (data.isEmpty) {
              initiateReturnBottomSheetBloc.addAndDeleteToGroup(
                  subOrderList: widget.suborderList, isLastProduct: true);
            }
          }
        },
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(
              height: 35,
              child: Row(
                children: [
                  StreamBuilder<bool>(
                      stream: initiateReturnBottomSheetBloc.checkBoxCtrl.stream,
                      initialData: false,
                      builder: (context, snapshot) {
                        return Container(
                          height: 18,
                          width: 18,
                          margin: const EdgeInsets.only(right: 10),
                          child: Checkbox(
                              value: widget.suborderList[index].isSelected,
                              activeColor: AppColors.primaryGreen,
                              onChanged: (value) {
                                //print(widget.suborderList[index].suborderNumber!);
                              }),
                        );
                      }),
                  Expanded(
                      child: appText(widget.suborderList[index].productName!,
                          color: AppColors.writingBlack,
                          fontWeight: FontWeight.w600,
                          fontFamily: "LatoSemibold",
                          fontSize: 14,
                          maxLine: 2)),
                  horizontalSizedBox(50),
                  ClipRRect(
                    borderRadius: const BorderRadius.all(Radius.circular(6)),
                    child: Container(
                      height: double.infinity,
                      width: 35,
                      decoration: const BoxDecoration(
                          borderRadius: BorderRadius.all(Radius.circular(6))),
                      child: extendedImage(
                        widget.suborderList[index].productImage!,
                        context,
                        100,
                        100,
                        cache: true,
                      ),
                    ),
                  )
                ],
              ),
            ),
            verticalSizedBox(10),
            subOrderTag(widget.suborderList[index].suborderStatus!)
          ],
        ),
      ),
    );
  }

//endregion

//region Group & initiate return
  Widget group() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
      child: Row(
        children: [
          sellerAllOrderActionButton(
              buttonName: "Group & initiate return",
              onPress: () {
                if (initiateReturnBottomSheetBloc.isGrouped) {
                  return initiateReturnBottomSheetBloc.addAndDeleteToGroup(
                      subOrderList: widget.suborderList);
                }
                initiateReturnBottomSheetBloc.groupSubOrders(
                    selectedSubOrders: CommonMethods.sellerSelectedSubOrderNumberList(
                        widget.suborderList),
                    subOrderList: widget.suborderList);
              }),
        ],
      ),
    );
  }

//endregion

//region Add shipping and tracking detail
  Widget addShippingAndTrackingDetail() {
    return StreamBuilder<bool>(
        stream: initiateReturnBottomSheetBloc.bottomSheetRefreshCtrl.stream,
        builder: (context, snapshot) {
          return Padding(
            padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                appText(
                  AppStrings.addShipping,
                  color: AppColors.writingColor2,
                  fontWeight: FontWeight.w600,
                  fontFamily: AppConstants.rRegular,
                  fontSize: 15,
                ),
                verticalSizedBox(20),
                groupName(),
                verticalSizedBox(20),
                deliveryDate(),
                verticalSizedBox(20),
                selectDeliveryMethod(),
              ],
            ),
          );
        });
  }

//endregion

//region Group name
  Widget groupName() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            appText("Group name",
                fontSize: 16,
                fontWeight: FontWeight.w400,
                color: AppColors.writingColor2,
                maxLine: 3,
                fontFamily: AppConstants.rRegular),
            SvgPicture.asset(AppImages.exclamation)
          ],
        ),
        verticalSizedBox(10),
        colorFilledTextField(
          context: context,
          textFieldCtrl: initiateReturnBottomSheetBloc.packageNameTextCtr,
          hintText: "Pack 1",
          textFieldMaxLine: 1,
          keyboardType: TextInputType.name,
          textInputAction: TextInputAction.done,
          // onChangeText: sellerOnBoardingBloc.onTextChange,
          regExp: AppConstants.acceptAll,
          fieldTextCapitalization: TextCapitalization.words,
          maxCharacter: 50,
        ),
      ],
    );
  }

//endregion

//region Delivery date
  Widget deliveryDate() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Row(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            appText("Max. estimated delivery date",
                fontSize: 16,
                fontWeight: FontWeight.w400,
                color: AppColors.writingColor2,
                maxLine: 3,
                fontFamily: AppConstants.rRegular),
            SvgPicture.asset(AppImages.exclamation)
          ],
        ),

        verticalSizedBox(10),
        //Calender
        Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            appText(
              initiateReturnBottomSheetBloc.estimatedDeliveryDate,
              color: AppColors.writingColor2,
              fontWeight: FontWeight.w600,
              fontFamily: AppConstants.rRegular,
              fontSize: 15,
            ),
            horizontalSizedBox(30),
            InkWell(
              onTap: (){
                initiateReturnBottomSheetBloc.onTapCalender();
              },
              child: Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                    color: AppColors.textFieldFill1,
                    borderRadius: BorderRadius.all(Radius.circular(10))),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    SvgPicture.asset(AppImages.calender),
                    horizontalSizedBox(10),
                    appText(
                      AppStrings.updateDeliveryDate,
                      color: AppColors.appBlack,
                      fontWeight: FontWeight.w600,
                      fontFamily: AppConstants.rRegular,
                      fontSize: 15,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

//endregion

//region Select product delivery method
  Widget selectDeliveryMethod() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        //Max estimated
        Row(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            appText("Select product delivery method",
                fontSize: 15,
                fontWeight: FontWeight.w600,
                color: AppColors.writingColor2,
                maxLine: 3,
                fontFamily: AppConstants.rRegular),
            horizontalSizedBox(10),
          ],
        ),
        //Self delivery
        InkWell(
          onTap: () {
            initiateReturnBottomSheetBloc.onSelectDeliveryMethod(true);
          },
          child: Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Checkbox(
                value: initiateReturnBottomSheetBloc.isSelfDelivery,
                onChanged: (value) {},
                activeColor: AppColors.primaryGreen,
              ),
              appText("Self-delivery by store",
                  fontSize: 15,
                  fontWeight: FontWeight.w600,
                  color: AppColors.writingColor2,
                  maxLine: 3,
                  fontFamily: AppConstants.rRegular),
            ],
          ),
        ),
        //Delivery by logistic
        InkWell(
          onTap: () {
            initiateReturnBottomSheetBloc.onSelectDeliveryMethod(false);
          },
          child: Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Checkbox(
                value: !initiateReturnBottomSheetBloc.isSelfDelivery,
                onChanged: (value) {},
                activeColor: AppColors.primaryGreen,
              ),
              appText("Delivery by logistics partner",
                  fontSize: 15,
                  fontWeight: FontWeight.w600,
                  color: AppColors.writingColor2,
                  maxLine: 3,
                  fontFamily: AppConstants.rRegular),
            ],
          ),
        ),

        ///Self delivery
        Visibility(
            visible: initiateReturnBottomSheetBloc.isSelfDelivery, child: selfDelivery()),

        ///Delivery by logistic
        Visibility(
            visible: !initiateReturnBottomSheetBloc.isSelfDelivery,
            child: deliveryByLogistic()),

        verticalSizedBox(10),

        ///Mark as shipped
        Visibility(
            visible: initiateReturnBottomSheetBloc.isShippingDetailVisible,
            child: markAsShipped())
      ],
    );
  }

//endregion

//region Self delivery
  Widget selfDelivery() {
    return const BySeller();
  }

//endregion

  //region Delivery by logistic
  Widget deliveryByLogistic() {
    return  const ByLogistics();
  }

  //endregion

//region Market as shipped button
  Widget markAsShipped() {
    return Column(
      children: [
        ///Additional notes
        Row(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            appText("Additional notes",
                fontSize: 16,
                fontWeight: FontWeight.w400,
                color: AppColors.writingColor2,
                maxLine: 3,
                fontFamily: AppConstants.rRegular),
            verticalSizedBox(10),
          ],
        ),
        verticalSizedBox(10),
        colorFilledTextField(
          context: context,
          textFieldCtrl: initiateReturnBottomSheetBloc.notesTextCtrl,
          hintText: "Note",
          textFieldMaxLine: 3,
          keyboardType: TextInputType.name,
          textInputAction: TextInputAction.done,
          // onChangeText: sellerOnBoardingBloc.onTextChange,
          regExp: AppConstants.acceptAll,
          fieldTextCapitalization: TextCapitalization.sentences,
          maxCharacter: 500,
        ),
        verticalSizedBox(20),

        Container(
          alignment: Alignment.centerLeft,
          child: appText("Marking return initiated will update your customer. ",
              fontSize: 14,
              fontWeight: FontWeight.w400,
              color: AppColors.writingColor2,
              textAlign: TextAlign.left,
              maxLine: 3,
              fontFamily: AppConstants.rRegular,
              style: FontStyle.italic),
        ),

        verticalSizedBox(10),

        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 80),
          child: Row(
            children: [
              sellerAllOrderActionButton(
                  buttonName: "Mark as Return Initiated", onPress: () {
                initiateReturnBottomSheetBloc.updateTracking();

              }),
            ],
          ),
        ),
      ],
    );
  }
//endregion

}
