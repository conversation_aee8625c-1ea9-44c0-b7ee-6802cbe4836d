import 'package:flutter/cupertino.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_strings.dart';

class MobileNumberScreenCommonWidgets {
  //region Button
  static Widget mobileNumberScreenButton({
    required dynamic onPress,
    required String buttonName,
  }) {
    return CupertinoButton(
        disabledColor: AppColors.primaryGreen,
        borderRadius: const BorderRadius.all(Radius.circular(9)),
        padding: const EdgeInsets.symmetric(horizontal: 10),
        color: AppColors.brandBlack,
        child: Center(
          child: Center(
              child: Text(
            AppStrings.resend,
            style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w700,
                color: AppColors.appWhite),
          )),
        ),
        onPressed: () {
          onPress();
        });
  }
  //endregion
}
