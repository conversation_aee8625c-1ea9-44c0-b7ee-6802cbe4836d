import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/seller/seller_accounts/seller_accounts_bloc.dart';
import 'package:swadesic/features/widgets/custom_image_container/custom_image_container.dart';
import 'package:swadesic/features/widgets/verified_badge.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:swadesic/features/communication/welcome_store_screen.dart';
import 'package:swadesic/features/buyers/buyer_home/home_access/home_access_bloc.dart';

class SellerAccountsScreen extends StatefulWidget {
  final String userReference;
  const SellerAccountsScreen({Key? key, required this.userReference})
      : super(key: key);

  @override
  _SellerAccountsScreenState createState() => _SellerAccountsScreenState();
}

class _SellerAccountsScreenState extends State<SellerAccountsScreen> {
  //region Bloc
  late SellerAccountsBloc sellerAccountsBloc;

  //endregion
  //region Init
  @override
  void initState() {
    //print("Seller Account Screen");
    sellerAccountsBloc = SellerAccountsBloc(context, widget.userReference);
    sellerAccountsBloc.init();
    super.initState();
  }

  //endregion

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: appBar(),
      backgroundColor: AppColors.appWhite,
      body: RefreshIndicator(
        onRefresh: () async {
          sellerAccountsBloc.getStoreList();
        },
        color: AppColors.brandBlack,
        child: body(),
      ),
    );
  }

  //region Appbar
  AppBar appBar() {
    return AppCommonWidgets.mainAppBar(
        context: context,
        isCustomTitle: false,
        title: AppStrings.accounts,
        isDefaultMenuVisible: false,
        isMembershipVisible: false,
        isDropdownVisible: true,
        isCartVisible: false,
        isRotateDropdownIcon: true,
        onTapDropdown: () {
          Navigator.pop(context);
        });
  }

  //endregion

  //region Body

  Widget body() {
    return StreamBuilder<SellerAccountsState>(
        stream: sellerAccountsBloc.sellerAccountCtrl.stream,
        initialData: SellerAccountsState.Loading,
        builder: (context, snapshot) {
          if (snapshot.data == SellerAccountsState.Loading) {
            return AppCommonWidgets.appCircularProgress();
          }
          if (snapshot.data == SellerAccountsState.Failed) {
            return AppCommonWidgets.errorWidget(
                errorMessage: AppStrings.unableToFetchAccountInfo,
                onTap: () {
                  sellerAccountsBloc.getLoggedInUserDetail();
                });
          }
          if (snapshot.data == SellerAccountsState.Success) {
            return SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  verticalSizedBox(20),
                  personal(),
                  verticalSizedBox(40),
                  businessText(),
                  // verticalSizedBox(10),
                  business(),
                  verticalSizedBox(40),
                  previewStoresText(),
                  verticalSizedBox(10),
                  previewStores(),
                  verticalSizedBox(40),
                  createStore(),
                  verticalSizedBox(20),
                ],
              ),
            );
          }
          return Container();
        });
  }

//endregion

//region Personal
  Widget personal() {
    var user = sellerAccountsBloc.getUserDetailsResponse.userDetail!;
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 10),
          child: Text(
            AppStrings.personal,
            style: AppTextStyle.settingHeading1(textColor: AppColors.appBlack),
          ),
        ),
        verticalSizedBox(15),
        InkWell(
          // padding: EdgeInsets.zero,
          onTap: () {
            sellerAccountsBloc.onTapUserProfile();
          },
          child: Container(
            color: AppConstants.appData.isUserView!
                ? AppColors.lightestGrey2
                : AppColors.appWhite,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
                  decoration: const BoxDecoration(
                    borderRadius: BorderRadius.all(Radius.circular(100)),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      CustomImageContainer(
                        height: 60,
                        width: 60,
                        imageUrl: user.icon,
                        imageType: CustomImageContainerType.user,
                      ),
                      // SizedBox(
                      //   height: 60,
                      //   width: 60,
                      //   child: ClipRRect(
                      //       borderRadius: const BorderRadius.all(Radius.circular(100)),
                      //       child: user.icon == null
                      //           ? SvgPicture.asset(AppImages.userPlaceHolder)
                      //           : extendedImage(user.icon!, context,
                      //           customPlaceHolder: AppImages.userPlaceHolder,
                      //           200, 200, cache: true)),
                      // ),
                      horizontalSizedBox(10),
                      Column(
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          //First and last name
                          Row(
                            mainAxisSize: MainAxisSize.min,
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text("${user.userName}",
                                  overflow: TextOverflow.ellipsis,
                                  style: AppConstants.appData.storeReference ==
                                          null
                                      ? AppTextStyle.access0(
                                              textColor: AppColors.appBlack)
                                          .copyWith(height: 0)
                                      : AppTextStyle.contentHeading0(
                                              textColor: AppColors.appBlack)
                                          .copyWith(height: 0)),
                              VerifiedBadge(
                                width: 15,
                                height: 15,
                                subscriptionType: user.subscriptionType,
                              )
                            ],
                          ),
                          verticalSizedBox(2),

                          Text(
                            "${user.firstName ?? ""} ${user.lastName ?? ""}",
                            overflow: TextOverflow.ellipsis,
                            style: AppTextStyle.contentText0(
                                textColor:
                                    AppConstants.appData.storeReference == null
                                        ? AppColors.writingBlack0
                                        : AppColors.writingBlack1),
                          ),
                          // verticalSizedBox(2),
                          // Text(
                          //   "Level: ",
                          //   style: TextStyle(
                          //     fontFamily: "LatoRegular",
                          //     fontSize: 14,
                          //     fontWeight: FontWeight.w400,
                          //     color: AppColors.darkGray,
                          //   ),
                          // ),
                        ],
                      ),
                      Expanded(child: horizontalSizedBox(10)),
                      RotatedBox(
                        quarterTurns: 3,
                        child: SvgPicture.asset(
                          width: 16,
                          AppImages.downArrow2,
                          color: AppColors.brandBlack,
                          fit: BoxFit.fill,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        )
      ],
    );
  }

//endregion

  //region Business Text
  Widget businessText() {
    return Row(
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 10),
          child: Text(
            AppStrings.business,
            style: AppTextStyle.settingHeading1(textColor: AppColors.appBlack),
          ),
        ),
        Expanded(child: horizontalSizedBox(10))
      ],
    );
  }
  
  //region Preview Stores Text
  Widget previewStoresText() {
    return Row(
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 10),
          child: Text(
            'Preview Stores',
            style: AppTextStyle.settingHeading1(textColor: AppColors.appBlack),
          ),
        ),
        Expanded(child: horizontalSizedBox(10))
      ],
    );
  }
  
  //region Preview Stores
  Widget previewStores() {
    if (sellerAccountsBloc.isLoadingPreviewStores) {
      return Padding(
        padding: EdgeInsets.symmetric(vertical: 20.0),
        child: Center(
          child: SizedBox(
            width: 24,
            height: 24,
            child: CircularProgressIndicator(
              strokeWidth: 2.0,
              valueColor: AlwaysStoppedAnimation<Color>(AppColors.brandBlack),
            ),
          ),
        ),
      );
    }
    
    if (sellerAccountsBloc.previewStoresError != null) {
      return Padding(
        padding: const EdgeInsets.symmetric(vertical: 20.0),
        child: Text(
          sellerAccountsBloc.previewStoresError!,
          style: AppTextStyle.contentText0(
            textColor: AppColors.red,
          ),
          textAlign: TextAlign.center,
        ),
      );
    }
    
    if (sellerAccountsBloc.previewStores.isEmpty) {
      return Padding(
        padding: const EdgeInsets.symmetric(vertical: 20.0),
        child: Text(
          'No preview stores available',
          style: AppTextStyle.contentText0(
            textColor: AppColors.writingBlack1,
          ),
          textAlign: TextAlign.center,
        ),
      );
    }
    
    return ListView.builder(
      padding: const EdgeInsets.symmetric(vertical: 10),
      shrinkWrap: true,
      itemCount: sellerAccountsBloc.previewStores.length,
      physics: const NeverScrollableScrollPhysics(),
      itemBuilder: (context, index) {
        final store = sellerAccountsBloc.previewStores[index];
        return Container(
          color: AppColors.appWhite,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              InkWell(
                onTap: () {
                  sellerAccountsBloc.onTapPreviewStore(store);
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 20,
                    vertical: 10,
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      CustomImageContainer(
                        height: 60,
                        width: 60,
                        imageUrl: store.previewStoreIcon,
                        imageType: CustomImageContainerType.store,
                      ),
                      horizontalSizedBox(10),
                      Column(
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            store.previewStoreName,
                            style: AppTextStyle.contentHeading0(
                              textColor: AppColors.appBlack,
                            ),
                          ),
                          verticalSizedBox(2),
                          Text(
                            '@${store.previewStorehandle}',
                            style: AppTextStyle.contentText0(
                              textColor: AppColors.writingBlack1,
                            ),
                          ),
                        ],
                      ),
                      Expanded(child: horizontalSizedBox(10)),
                      RotatedBox(
                        quarterTurns: 3,
                        child: SvgPicture.asset(
                          AppImages.downArrow2,
                          color: AppColors.brandBlack,
                          fit: BoxFit.fill,
                          width: 16,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  //endregion

//region Business
  Widget business() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Visibility(
            visible: sellerAccountsBloc.storeListResponse.storeList!.isEmpty,
            child: Container(
                margin: const EdgeInsets.only(top: 40),
                child: Text(
                  AppStrings.startYourStoreAt,
                  style: AppTextStyle.heading2Regular(
                      textColor: AppColors.appBlack),
                ))),
        Visibility(
          visible: sellerAccountsBloc.storeListResponse.storeList!.isNotEmpty,
          child: ListView.builder(
              padding: const EdgeInsets.symmetric(vertical: 10),
              shrinkWrap: true,
              itemCount: sellerAccountsBloc.storeListResponse.storeList!.length,
              physics: const NeverScrollableScrollPhysics(),
              itemBuilder: (context, index) {
                return Container(
                  ///If the store is selected then change color
                  color: AppConstants.appData.isStoreView! &&
                          AppConstants.appData.storeReference ==
                              sellerAccountsBloc.storeListResponse
                                  .storeList![index].storeReference
                      ? AppColors.lightestGrey2
                      : AppColors.appWhite,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      InkWell(
                        // padding: EdgeInsets.zero,
                        onTap: () {
                          sellerAccountsBloc.onTapStore(sellerAccountsBloc
                              .storeListResponse.storeList![index]);
                        },
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 20, vertical: 10),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              CustomImageContainer(
                                height: 60,
                                width: 60,
                                imageUrl: sellerAccountsBloc
                                    .storeListResponse.storeList![index].icon,
                                imageType: CustomImageContainerType.store,
                              ),

                              // SizedBox(
                              //   height: 60,
                              //   width: 60,
                              //   child: ClipRRect(
                              //       borderRadius: const BorderRadius.all(Radius.circular(15)),
                              //       child: sellerAccountsBloc.storeListResponse.storeList![index].icon == null
                              //           ? SvgPicture.asset(AppImages.storePlaceHolder)
                              //           : extendedImage(sellerAccountsBloc.storeListResponse.storeList![index].icon!,
                              //           customPlaceHolder: AppImages.storePlaceHolder,
                              //           context, 200, 200,
                              //           cache: true)),
                              // ),
                              horizontalSizedBox(10),
                              Column(
                                mainAxisSize: MainAxisSize.min,
                                mainAxisAlignment: MainAxisAlignment.center,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    mainAxisSize: MainAxisSize.min,
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                          sellerAccountsBloc.storeListResponse
                                              .storeList![index].storehandle!,
                                          overflow: TextOverflow.ellipsis,
                                          style: AppConstants
                                                      .appData.storeReference ==
                                                  sellerAccountsBloc
                                                      .storeListResponse
                                                      .storeList![index]
                                                      .storeReference
                                              ? AppTextStyle.access0(
                                                      textColor:
                                                          AppColors.appBlack)
                                                  .copyWith(height: 0)
                                              : AppTextStyle.contentHeading0(
                                                      textColor:
                                                          AppColors.appBlack)
                                                  .copyWith(height: 0)),
                                      VerifiedBadge(
                                        width: 15,
                                        height: 15,
                                        subscriptionType: sellerAccountsBloc
                                            .storeListResponse
                                            .storeList![index]
                                            .subscriptionType,
                                      )
                                    ],
                                  ),
                                  verticalSizedBox(2),
                                  //Category
                                  Text(
                                      sellerAccountsBloc.storeListResponse
                                          .storeList![index].categoryName!,
                                      style: AppTextStyle.contentText0(
                                          textColor: AppConstants
                                                      .appData.storeReference ==
                                                  sellerAccountsBloc
                                                      .storeListResponse
                                                      .storeList![index]
                                                      .storeReference
                                              ? AppColors.writingBlack0
                                              : AppColors.writingBlack1)),
                                  // verticalSizedBox(2),
                                  // const Text(
                                  //   "Level: 40",
                                  //   style: TextStyle(
                                  //     fontFamily: "LatoRegular",
                                  //     fontSize: 14,
                                  //     fontWeight: FontWeight.w400,
                                  //     color: AppColors.darkGray,
                                  //   ),
                                  // ),
                                ],
                              ),
                              Expanded(child: horizontalSizedBox(10)),
                              RotatedBox(
                                quarterTurns: 3,
                                child: SvgPicture.asset(
                                  AppImages.downArrow2,
                                  color: AppColors.brandBlack,
                                  fit: BoxFit.fill,
                                  width: 16,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      // divider(),
                    ],
                  ),
                );
              }),
        ),
      ],
    );
  }

//endregion

//region Create Account
  Widget createStore() {
    return StreamBuilder<SellerAccountsState>(
        stream: sellerAccountsBloc.sellerAccountCtrl.stream,
        initialData: SellerAccountsState.Empty,
        builder: (context, snapshot) {
          return Column(
            children: [
              CupertinoButton(
                  borderRadius: BorderRadius.circular(100),
                  color: AppColors.brandBlack,
                  child: Text(AppStrings.createNewStore,
                      style:
                          AppTextStyle.access0(textColor: AppColors.appWhite)),
                  onPressed: () {
                    Navigator.push(
                      AppConstants.userStoreCommonBottomNavigationContext,
                      MaterialPageRoute(
                        builder: (context) => WelcomeStoreScreen(
                          inviteCode: AppConstants.appData.userReference!,
                          homeAccessBloc: HomeAccessBloc(context),
                        ),
                      ),
                    );
                  }),
              // verticalSizedBox(10),
              // Text(
              //   AppStrings.howStoreHelpYou,
              //   style: AppTextStyle.contentText0(textColor: AppColors.brandGreen),
              // ),
            ],
          );
        });
  }
//endregion
}
