import 'package:flutter/material.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_text_style.dart';

class ShareAccessBottomSheet extends StatelessWidget {
  final List<Map<String, dynamic>> accessOptions;
  const ShareAccessBottomSheet({super.key, required this.accessOptions});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.appWhite,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Bottom sheet handle bar
          Container(
            margin: const EdgeInsets.only(top: 12, bottom: 8),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: AppColors.borderColor0,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          // Options list
          Flexible(
            child: ListView.separated(
              shrinkWrap: true,
              physics: const ClampingScrollPhysics(),
              padding: const EdgeInsets.only(
                top: 8,
                bottom: 20,
                left: 20,
                right: 20,
              ),
              itemCount: accessOptions.length,
              separatorBuilder: (context, index) => Divider(
                height: 1,
                thickness: 1,
                color: AppColors.borderColor0,
              ),
              itemBuilder: (context, index) => _buildOption(
                title: accessOptions[index]['title'],
                onTap: accessOptions[index]['onTap'],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOption({
    required String title,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 16),
        child: Text(
          title,
          style: AppTextStyle.contentHeading0(
            textColor: AppColors.appBlack,
          ),
        ),
      ),
    );
  }
}
