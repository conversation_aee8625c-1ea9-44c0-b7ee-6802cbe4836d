import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:swadesic/features/common_buyer_seller_screen/your_invitees/your_invitees_bloc.dart';
import 'package:swadesic/features/widgets/invite_more_frends/invite_more_friends.dart';
import 'package:swadesic/features/widgets/user_or_store_card/user_or_store_card.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

class YourInvitees extends StatefulWidget {
  const YourInvitees({super.key});

  @override
  State<YourInvitees> createState() => _YourInviteesState();
}

class _YourInviteesState extends State<YourInvitees>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  //region Bloc
  late YourInviteesBloc yourInviteesBloc;
  //endregion

  //region Init
  @override
  void initState() {
    yourInviteesBloc = YourInviteesBloc(context);
    yourInviteesBloc.init();
    super.initState();
  }

  //endregion
  @override
  Widget build(BuildContext context) {
    return SafeArea(child: body());
  }

  //region Body
  Widget body() {
    return StreamBuilder<YourInviteesState>(
        stream: yourInviteesBloc.inviteeCountsCtrl.stream,
        initialData: YourInviteesState.Loading,
        builder: (context, snapshot) {
          //Loading
          if (snapshot.data == YourInviteesState.Loading) {
            return Container(
              height: MediaQuery.of(context).size.height,
              alignment: Alignment.center,
              child: AppCommonWidgets.appCircularProgress(),
            );
          }
          //Success
          if (snapshot.data == YourInviteesState.Success) {
            return Padding(
              padding: const EdgeInsets.symmetric(horizontal: 10),
              child: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    invitedBy(),
                    const InviteMoreFriends(),
                    invitedUserAndStore(),
                  ],
                ),
              ),
            );
          }

          //Failed
          return AppCommonWidgets.errorWidget(
              errorMessage: AppStrings.unableToLoadInvitedCount,
              height: MediaQuery.of(context).size.height,
              onTap: () {
                yourInviteesBloc.init();
              });
        });
  }
//endregion

//region Invited by
  Widget invitedBy() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
      decoration: AppCommonWidgets()
          .borderRoundGrayContainer(context: context)
          .copyWith(),
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 25),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
                child: Text(
                    "${yourInviteesBloc.inviteeCounts.invitedUsersCount} users and ${yourInviteesBloc.inviteeCounts.invitedStoresCount} stores",
                    style: TextStyle(
                        fontSize: 30,
                        fontWeight: FontWeight.w600,
                        fontFamily: "RobotoRegular",
                        color: AppColors.appBlack))),
            const SizedBox(height: 5),
            Row(
              children: [
                Text(
                  "Invited by you",
                  style: AppTextStyle.contentHeading0(
                      textColor: AppColors.appBlack),
                ),
                const Expanded(child: const SizedBox()),
                Text(
                  "${yourInviteesBloc.inviteCode}",
                  overflow: TextOverflow.ellipsis,
                  maxLines: 1,
                  style:
                      AppTextStyle.pageHeading(textColor: AppColors.brandBlack),
                ),
                const SizedBox(width: 10),
                SizedBox(
                    height: 22,
                    width: 22,
                    child: CupertinoButton(
                        padding: EdgeInsets.zero,
                        onPressed: () {
                          CommonMethods.copyText(
                              context, yourInviteesBloc.inviteCode);
                        },
                        child: Image.asset(
                          AppImages.copy,
                          height: 22,
                          width: 22,
                        ))),
                const SizedBox(width: 10),
                SizedBox(
                  height: 22,
                  width: 22,
                  child: CupertinoButton(
                      padding: EdgeInsets.zero,
                      onPressed: () {
                        CommonMethods.share(
                            "${AppConstants.domainName}?ref=${yourInviteesBloc.inviteCode}");
                      },
                      child: Image.asset(
                        AppImages.shareThreeLine,
                        height: 22,
                        width: 22,
                      )),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
//endregion

  //region Invited user and stores
  Widget invitedUserAndStore() {
    return StreamBuilder<YourInviteesState>(
        stream: yourInviteesBloc.yourInviteesCtrl.stream,
        initialData: YourInviteesState.Loading,
        builder: (context, snapshot) {
          //Loading
          if (snapshot.data == YourInviteesState.Loading) {
            return Container(
              height: MediaQuery.of(context).size.height / 2,
              alignment: Alignment.center,
              child: AppCommonWidgets.appCircularProgress(),
            );
          }
          //Success
          if (snapshot.data == YourInviteesState.Success) {
            return Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Visibility(
                    visible: yourInviteesBloc.invitedUsers.isNotEmpty,
                    child: user()),
                Visibility(
                    visible: yourInviteesBloc.invitedStores.isNotEmpty,
                    child: store()),
              ],
            );
          }

          //Failed
          return AppCommonWidgets.errorWidget(
              errorMessage: AppStrings.unableToLoadInvitedUserAndStore,
              height: MediaQuery.of(context).size.height / 2,
              onTap: () {
                yourInviteesBloc.init();
              });
        });
  }
  //endregion

//region User
  Widget user() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "Users",
          style: AppTextStyle.sectionHeading(textColor: AppColors.appBlack),
        ),
        ListView.builder(
            padding: const EdgeInsets.only(bottom: 30),
            physics: const NeverScrollableScrollPhysics(),
            shrinkWrap: true,
            itemCount: yourInviteesBloc.invitedUsers.length,
            itemBuilder: (context, index) {
              return UserOrStoreCard(
                reference: yourInviteesBloc.invitedUsers[index].reference!,
                followStatus:
                    yourInviteesBloc.invitedUsers[index].followStatus!,
                handleOrUserName: yourInviteesBloc.invitedUsers[index].handle!,
                onTapCard: () {
                  yourInviteesBloc.onTapIcon(
                      invitedUsersOrStore:
                          yourInviteesBloc.invitedUsers[index]);
                },
                onTapFollowButton: () {
                  yourInviteesBloc.onTapFollowAndSupport(
                      invitedUserAndStore:
                          yourInviteesBloc.invitedUsers[index]);
                },
                // subTitle: Text("${DateTime.parse(yourInviteesBloc.invitedUsers[index].createdDate!)
                subTitle: Text(
                  "${DateFormat("yyyy-MM-dd").format(DateFormat("dd:MM:yyyy HH:mm:ss").parse(yourInviteesBloc.invitedUsers[index].createdDate!))}",
                  overflow: TextOverflow.ellipsis,
                  maxLines: 1,
                  style:
                      AppTextStyle.contentText0(textColor: AppColors.appBlack),
                ),
                imageUrl: yourInviteesBloc.invitedUsers[index].icon,
              );
            }),
      ],
    );
  }
//endregion

//region Store
  Widget store() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "Store",
          style: AppTextStyle.sectionHeading(textColor: AppColors.appBlack),
        ),
        ListView.builder(
            padding: const EdgeInsets.only(bottom: 30),
            physics: const NeverScrollableScrollPhysics(),
            shrinkWrap: true,
            itemCount: yourInviteesBloc.invitedStores.length,
            itemBuilder: (context, index) {
              return UserOrStoreCard(
                reference: yourInviteesBloc.invitedStores[index].reference!,
                followStatus:
                    yourInviteesBloc.invitedStores[index].followStatus!,
                handleOrUserName: yourInviteesBloc.invitedStores[index].handle!,
                onTapCard: () {
                  yourInviteesBloc.onTapIcon(
                      invitedUsersOrStore:
                          yourInviteesBloc.invitedStores[index]);
                },
                onTapFollowButton: () {
                  yourInviteesBloc.onTapFollowAndSupport(
                      invitedUserAndStore:
                          yourInviteesBloc.invitedStores[index]);
                },
                // subTitle: Text(DateTime.parse(yourInviteesBloc.invitedUsers[index].createdDate!)
                //     .toIso8601String()
                //     .split('T')
                //     .first,
                subTitle: Text(
                  "${DateFormat("yyyy-MM-dd").format(DateFormat("dd:MM:yyyy HH:mm:ss").parse(yourInviteesBloc.invitedStores[index].createdDate!))}",
                  overflow: TextOverflow.ellipsis,
                  maxLines: 1,
                  style:
                      AppTextStyle.contentText0(textColor: AppColors.appBlack),
                ),
                imageUrl: yourInviteesBloc.invitedStores[index].icon,
              );
            }),
      ],
    );
  }
//endregion
}
