import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/buyers/buyer_home/buyer_home_bloc.dart';
import 'package:swadesic/features/seller/membership/membership_bloc.dart';
import 'package:swadesic/model/membership_responses/friends_invites_response.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

import '../../../../util/app_colors.dart';

class AlreadyJoinedBottomSheet extends StatelessWidget {
  final FriendsInfo friendsInfo;
  final MembershipBloc membershipBloc;
  const AlreadyJoinedBottomSheet({Key? key, required this.friendsInfo, required this.membershipBloc}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 10,horizontal: 10),
      width:MediaQuery.of(context).size.width,
      decoration: BoxDecoration(
          color: AppColors.appWhite,
          border: Border.all(color: AppColors.lightStroke,),
          borderRadius:const BorderRadius.only(topLeft: Radius.circular(25),topRight:Radius.circular(25), )
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          //Send invite
          Padding(
            padding: EdgeInsets.only(left: 10),
            child: Text("Send invite",
              style:  TextStyle(
                fontFamily: "LatoSemibold",
                fontSize: 15,
                fontWeight: FontWeight.w600,
                color:AppColors.writingColor2,
              ),
            ),
          ),
          verticalSizedBox(20),
          //Member
          friendsInfo.inviteTypeByThisUser=="MEMBER"||friendsInfo.inviteTypeByThisUser=="CORPORATE_MEMBER"? InkWell(
            onTap: (){
              //Close bottom sheet
              Navigator.of(context).pop();
              //Add message
              var message = "Hey ${friendsInfo.name}.. I have a Membership invite to Swadesic and I want you to join 🙂 "
                  "I added you with this number ${friendsInfo.phonenumber}, "
                  "so make sure you sign up using this. Here is your Membership link with invite code- ${friendsInfo.inviteCodeByThisUser!}";
              //Open share
              CommonMethods.share(message);

              // membershipBloc.createInviteCode(friendsInfo.phonenumber!,"MEMBER",friendsInfo.name);

            },
            child: Container(
              width: double.infinity,
              alignment: Alignment.center,
              padding: const EdgeInsets.symmetric(vertical: 15),
              decoration: BoxDecoration(
                  color: AppColors.textFieldFill1,
                  borderRadius: BorderRadius.all(Radius.circular(10))
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  SvgPicture.asset(AppImages.starBlank),
                  horizontalSizedBox(10),
                  Text("Member invite already sent, send again",
                    style:  TextStyle(
                      fontFamily: "LatoBold",
                      fontSize: 15,
                      fontWeight: FontWeight.w700,
                      color:AppColors.writingColor2,
                    ),
                  ),


                ],
              ),
            ),
          ):InkWell(
            onTap: ()async{
              Navigator.of(context).pop();
             await membershipBloc.deleteInviteCode(friendsInfo.inviteCodeByThisUser!);
              await membershipBloc.createInviteCode(friendsInfo.phonenumber!,"MEMBER",friendsInfo.name);

            },
            child: Container(
              width: double.infinity,
              alignment: Alignment.center,
              padding: const EdgeInsets.symmetric(vertical: 15),
              decoration: BoxDecoration(
                  color: AppColors.textFieldFill1,
                  borderRadius: BorderRadius.all(Radius.circular(10))
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  SvgPicture.asset(AppImages.starBlank),
                  horizontalSizedBox(10),
                  Text("Send member invite  [${BuyerHomeBloc.userDetailsResponse.userDetail!.memberInviteBalance!} left]",
                    style: TextStyle(
                      fontFamily: "LatoBold",
                      fontSize: 15,
                      fontWeight: FontWeight.w700,
                      color:AppColors.writingColor2,
                    ),
                  ),
                ],
              ),
            ),
          ),
          verticalSizedBox(10),
          //Member
          friendsInfo.inviteTypeByThisUser=="SELLER"||friendsInfo.inviteTypeByThisUser=="CORPORATE_SELLER"? InkWell(
            onTap: (){
              //Close bottom sheet
              Navigator.of(context).pop();
              //Add message
              var message = "Hey ${friendsInfo.name}.. I have a Seller Membership invite to Swadesic and I want you to join 🙂 "
                  "I added you with this number ${friendsInfo.phonenumber}, "
                  "so make sure you sign up using this. Here is your Seller Membership link with invite code- ${friendsInfo.inviteCodeByThisUser!}";
              //Open share
              CommonMethods.share(message);
            },
            child: Container(
              width: double.infinity,
              alignment: Alignment.center,
              padding: const EdgeInsets.symmetric(vertical: 15),
              decoration: BoxDecoration(
                  color: AppColors.textFieldFill1,
                  borderRadius: BorderRadius.all(Radius.circular(10))
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  SvgPicture.asset(AppImages.starBlank),
                  horizontalSizedBox(10),
                  Text("Seller invite already sent, send again",
                    style:  TextStyle(
                      fontFamily: "LatoBold",
                      fontSize: 15,
                      fontWeight: FontWeight.w700,
                      color:AppColors.writingColor2,
                    ),
                  ),


                ],
              ),
            ),
          ):InkWell(
            onTap: (){
              Navigator.of(context).pop();
              membershipBloc.createInviteCode(friendsInfo.phonenumber!,"SELLER",friendsInfo.name);
            },
            child: Container(
              width: double.infinity,
              alignment: Alignment.center,
              padding: const EdgeInsets.symmetric(vertical: 15),
              decoration: BoxDecoration(
                  color: AppColors.textFieldFill1,
                  borderRadius: BorderRadius.all(Radius.circular(10))
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  SvgPicture.asset(AppImages.starBlank),
                  horizontalSizedBox(10),
                  Text("Send seller invite  [${BuyerHomeBloc.userDetailsResponse.userDetail!.sellerInviteBalance!} left]",
                    style: TextStyle(
                      fontFamily: "LatoBold",
                      fontSize: 15,
                      fontWeight: FontWeight.w700,
                      color:AppColors.writingColor2,
                    ),
                  ),


                ],
              ),
            ),
          )

        ],
      ),
    );
  }
}
