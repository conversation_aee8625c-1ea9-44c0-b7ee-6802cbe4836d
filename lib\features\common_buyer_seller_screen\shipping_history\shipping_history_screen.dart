import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/common_buyer_seller_screen/shipping_history/shipping_history_bloc.dart';
import 'package:swadesic/model/shipping_history/shipping_history_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_drop_down/app_drop_down.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

class ShippingHistoryScreen extends StatefulWidget {
  final String pNumber;
  final String orderNumber;
  final String dropDownTitle;
  final bool isSeller;
  const ShippingHistoryScreen(
      {Key? key,
      required this.pNumber,
      required this.orderNumber,
      required this.dropDownTitle,
      this.isSeller = true})
      : super(key: key);

  @override
  State<ShippingHistoryScreen> createState() => _ShippingHistoryScreenState();
}

class _ShippingHistoryScreenState extends State<ShippingHistoryScreen> {
  //region Bloc
  late ShippingHistoryBloc shippingHistoryBloc;
  //endregion

  //region Init
  @override
  void initState() {
    shippingHistoryBloc =
        ShippingHistoryBloc(context, widget.pNumber, widget.orderNumber);
    shippingHistoryBloc.init();
    // TODO: implement initState
    super.initState();
  }
  //endregion

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
        onTap: () {
          shippingHistoryBloc.historyIndex = null;
          shippingHistoryBloc.shippingHistoryCtrl.sink
              .add(ShippingHistoryState.Success);
        },
        child: shippingHistoryList());
  }

  //region Shipping history list
  Widget shippingHistoryList() {
    return StreamBuilder<ShippingHistoryState>(
        stream: shippingHistoryBloc.shippingHistoryCtrl.stream,
        builder: (context, snapshot) {
          if (snapshot.data == ShippingHistoryState.Success) {
            return widget.isSeller
                ? AppDropDown(
                    dropDownName: widget.dropDownTitle,
                    dropDownWidget: Padding(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 10, vertical: 10),
                      child: Column(
                        children: [
                          ListView.builder(
                              physics: const NeverScrollableScrollPhysics(),
                              shrinkWrap: true,
                              itemCount: shippingHistoryBloc
                                  .shippingHistoryResponse.historyList!.length,
                              itemBuilder: (buildContext, index) {
                                return Column(
                                  mainAxisSize: MainAxisSize.min,
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    shippingHistoryBloc.selectedHistoryId ==
                                            shippingHistoryBloc
                                                .shippingHistoryResponse
                                                .historyList![index]
                                                .shippingUpdateId
                                        ? editDetail()
                                        : shippingHistoryBox(
                                            history: shippingHistoryBloc
                                                .shippingHistoryResponse
                                                .historyList![index],
                                            index: index),
                                    Padding(
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 10, vertical: 2),
                                      child: RotatedBox(
                                        quarterTurns: 1,
                                        child: Container(
                                          height: 2,
                                          width: 10,
                                          color: AppColors.appBlack,
                                        ),
                                      ),
                                    ),
                                    Padding(
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 10, vertical: 2),
                                      child: RotatedBox(
                                        quarterTurns: 1,
                                        child: Container(
                                          height: 2,
                                          width: 10,
                                          color: AppColors.appBlack,
                                        ),
                                      ),
                                    )
                                  ],
                                );
                              }),
                          Visibility(
                              visible:
                                  !shippingHistoryBloc.isAddUpdateFieldVisible,
                              child: addAndUpdateButton()),
                          Visibility(
                              visible:
                                  shippingHistoryBloc.isAddUpdateFieldVisible,
                              child: updateDetailTextField()),
                        ],
                      ),
                    ),
                    initialExpand: true,
                    collapsedWidget: const SizedBox(),
                  )
                : AppDropDown(
                    dropDownName: widget.dropDownTitle,
                    dropDownWidget: Padding(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 10, vertical: 10),
                      child: Column(
                        children: [
                          ListView.builder(
                              physics: const NeverScrollableScrollPhysics(),
                              shrinkWrap: true,
                              itemCount: shippingHistoryBloc
                                  .shippingHistoryResponse.historyList!.length,
                              itemBuilder: (buildContext, index) {
                                return Column(
                                  mainAxisSize: MainAxisSize.min,
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    shippingHistoryBox(
                                        history: shippingHistoryBloc
                                            .shippingHistoryResponse
                                            .historyList![index],
                                        index: index),
                                    index ==
                                            shippingHistoryBloc
                                                    .shippingHistoryResponse
                                                    .historyList!
                                                    .length -
                                                1
                                        ? const SizedBox()
                                        : Column(
                                            mainAxisSize: MainAxisSize.min,
                                            children: [
                                              Padding(
                                                padding:
                                                    const EdgeInsets.symmetric(
                                                        horizontal: 10,
                                                        vertical: 2),
                                                child: RotatedBox(
                                                  quarterTurns: 1,
                                                  child: Container(
                                                    height: 2,
                                                    width: 10,
                                                    color: AppColors.appBlack,
                                                  ),
                                                ),
                                              ),
                                              Padding(
                                                padding:
                                                    const EdgeInsets.symmetric(
                                                        horizontal: 10,
                                                        vertical: 2),
                                                child: RotatedBox(
                                                  quarterTurns: 1,
                                                  child: Container(
                                                    height: 2,
                                                    width: 10,
                                                    color: AppColors.appBlack,
                                                  ),
                                                ),
                                              )
                                            ],
                                          )
                                  ],
                                );
                              }),
                        ],
                      ),
                    ),
                    initialExpand: true,
                    collapsedWidget: const SizedBox(),
                  );
          }
          return const SizedBox();
        });
  }
  //endregion

//region Shipping history box
  Widget shippingHistoryBox({required History history, required int index}) {
    return Stack(
      children: [
        Container(
          decoration: BoxDecoration(
              color: AppColors.textFieldFill1,
              borderRadius: BorderRadius.all(Radius.circular(10))),
          child: Row(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                margin: const EdgeInsets.only(left: 10, top: 15),
                child: SvgPicture.asset(
                  AppImages.dot,
                  width: 10,
                  height: 10,
                  color: AppColors.appBlack10,
                ),
              ),
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.only(top: 10, left: 10, bottom: 10),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        "${history.title}",
                        style: AppTextStyle.contentHeading0(
                            textColor: AppColors.appBlack),
                      ),
                      // appText("${history.title}",
                      //     fontSize: 14, fontWeight: FontWeight.w600, color: AppColors.appBlack, maxLine: 5, fontFamily: AppConstants.rRegular),
                      //
                      Padding(
                        padding: const EdgeInsets.symmetric(vertical: 5),
                        child: appText(
                          "${history.description}",
                          fontSize: 14,
                          fontWeight: FontWeight.w400,
                          color: AppColors.appBlack,
                          maxLine: 5,
                          fontFamily: AppConstants.rRegular,
                        ),
                      ),
                      Text(
                        history.date!,
                        style: AppTextStyle.contentText0(
                            textColor: AppColors.appBlack),
                      ),
                    ],
                  ),
                ),
              ),
              widget.isSeller
                  ? Visibility(
                      visible: history.isEditable!,
                      child: historyMenu(history: history))
                  : const SizedBox()

              // InkWell(
              //   onTap: () {
              //     history.isEditable!
              //         ? shippingHistoryBloc.onTapOptions(history: history, index: index)
              //         : CommonMethods.snackBar("Can't edit or delete", context);
              //     //shippingHistoryBloc.onTapEditDelete(title: history.title!, desc: history.description!, historyId: history.shippingUpdateId!);
              //   },
              //   child: Container(
              //       padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 3),
              //       child: SvgPicture.asset(
              //         AppImages.drawerIcon,
              //         color: AppColors.appBlack,
              //       )),
              // )
            ],
          ),
        ),
        Positioned(
          top: 0,
          right: 0,
          child: Visibility(
            visible: shippingHistoryBloc.historyIndex == index,
            child: historyMenu(history: history),
          ),
        )
      ],
    );
  }
//endregion

  ///Not in use
  //region Edit and delete option
  // Widget editDelete({required History history}) {
  //   return Padding(
  //     padding: const EdgeInsets.only(right: 30),
  //     child: Container(
  //         width: 150,
  //         decoration: BoxDecoration(
  //           color: AppColors.white,
  //           border: Border.all(color: AppColors.lightGray.withOpacity(0.2)),
  //           boxShadow: [
  //             BoxShadow(
  //               offset: const Offset(1, 1),
  //               blurRadius: 5,
  //               color: AppColors.appBlack.withOpacity(0.2),
  //             ),
  //           ],
  //         ),
  //         child: Column(
  //           mainAxisSize: MainAxisSize.min,
  //           mainAxisAlignment: MainAxisAlignment.center,
  //           crossAxisAlignment: CrossAxisAlignment.start,
  //           children: [
  //             //Seller
  //             InkWell(
  //               onTap: () {
  //                 shippingHistoryBloc.onTapEdit(history: history);
  //               },
  //               child: Container(
  //                   width: double.infinity,
  //                   padding: const EdgeInsets.all(10),
  //                   child: appText(
  //                     "Edit",
  //                     color: AppColors.writingColor2,
  //                     fontSize: 14,
  //                     fontFamily: AppConstants.rRegular,
  //                     fontWeight: FontWeight.w700,
  //                   )),
  //             ),
  //             divider(),
  //             InkWell(
  //               onTap: () {
  //                 shippingHistoryBloc.deleteHistory(historyId: history.shippingUpdateId!);
  //               },
  //               child: Container(
  //                   width: double.infinity,
  //                   padding: const EdgeInsets.all(10),
  //                   child: appText(
  //                     "Delete",
  //                     color: AppColors.writingColor2,
  //                     fontSize: 14,
  //                     fontFamily: AppConstants.rRegular,
  //                     fontWeight: FontWeight.w700,
  //                   )),
  //             ),
  //           ],
  //         )),
  //   );
  // }
  //endregion

//region Add and update
  Widget addAndUpdateButton() {
    return InkWell(
      onTap: () {
        shippingHistoryBloc.onTapAddUpdate();
      },
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Container(
            padding:
                const EdgeInsets.only(left: 10, right: 15, top: 10, bottom: 10),
            decoration: BoxDecoration(
                color: AppColors.brandBlack,
                borderRadius: BorderRadius.all(Radius.circular(10))),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                SvgPicture.asset(
                  AppImages.dot,
                  width: 10,
                  height: 10,
                  color: AppColors.appWhite,
                ),
                horizontalSizedBox(10),
                appText("Add an update",
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: AppColors.appWhite,
                    maxLine: 5,
                    fontFamily: AppConstants.rRegular),
              ],
            ),
          ),
          Expanded(child: horizontalSizedBox(10))
        ],
      ),
    );
  }
//endregion

//region Update Detail TextField
  Widget updateDetailTextField() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.end,
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        Container(
          //margin: const EdgeInsets.only(bottom: 10),
          padding: const EdgeInsets.all(10),
          decoration: BoxDecoration(
              color: AppColors.textFieldFill1,
              borderRadius: BorderRadius.all(Radius.circular(10))),
          child: Row(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.only(top: 5),
                child: SvgPicture.asset(
                  AppImages.dot,
                  width: 10,
                  height: 10,
                  color: AppColors.appBlack10,
                ),
              ),
              horizontalSizedBox(5),
              Expanded(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    TextFormField(
                      controller: shippingHistoryBloc.titleTextCtrl,
                      textInputAction: TextInputAction.next,
                      keyboardType: TextInputType.name,
                      textCapitalization: TextCapitalization.sentences,
                      style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: AppColors.appBlack,
                          fontFamily: AppConstants.rRegular),
                      decoration: InputDecoration.collapsed(
                          hintText: 'Update title',
                          hintStyle: TextStyle(
                              fontSize: 14,
                              fontFamily: AppConstants.rRegular,
                              fontWeight: FontWeight.w400,
                              color: AppColors.writingColor3)),
                    ),
                    verticalSizedBox(5),
                    TextFormField(
                      controller: shippingHistoryBloc.descTextCtrl,
                      textInputAction: TextInputAction.done,
                      keyboardType: TextInputType.name,
                      maxLines: 3,
                      textCapitalization: TextCapitalization.sentences,
                      style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w400,
                          color: AppColors.appBlack,
                          fontFamily: AppConstants.rRegular),
                      decoration: InputDecoration.collapsed(
                          hintText: 'Add a description & details if any',
                          hintStyle: TextStyle(
                              fontSize: 14,
                              fontFamily: AppConstants.rRegular,
                              fontWeight: FontWeight.w400,
                              color: AppColors.writingColor3)),
                    ),
                    verticalSizedBox(5),
                    appText(shippingHistoryBloc.currentDateTime,
                        fontSize: 12,
                        fontWeight: FontWeight.w400,
                        color: AppColors.appBlack,
                        maxLine: 1,
                        fontFamily: AppConstants.rRegular),
                  ],
                ),
              ),
              //Expanded(child: horizontalSizedBox(10)),
              Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 10, vertical: 3),
                  child: SvgPicture.asset(
                    AppImages.drawerIcon,
                    color: AppColors.appBlack,
                  ))
            ],
          ),
        ),
        verticalSizedBox(15),
        StreamBuilder<bool>(
          stream: shippingHistoryBloc.addButtonLoadingCtrl.stream,
          initialData: false,
          builder: (context, snapshot) {
            bool isLoading = snapshot.data ?? false;

            return CupertinoButton(
              onPressed: isLoading
                  ? null
                  : () {
                      shippingHistoryBloc.onTapAdd();
                    },
              padding: EdgeInsets.zero,
              child: Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 50, vertical: 10),
                decoration: BoxDecoration(
                  color: isLoading
                      ? AppColors.brandBlack.withOpacity(0.7)
                      : AppColors.brandBlack,
                  borderRadius: const BorderRadius.all(Radius.circular(10)),
                ),
                child: SizedBox(
                  height: 20, // Fixed height to prevent size changes
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      isLoading
                          ? SizedBox(
                              height: 20,
                              width: 20,
                              child: SpinKitFadingCircle(
                                color: AppColors.appWhite,
                                size: 20,
                              ),
                            )
                          : appText("Add",
                              fontSize: 14,
                              fontWeight: FontWeight.w600,
                              color: AppColors.appWhite,
                              maxLine: 5,
                              fontFamily: AppConstants.rRegular),
                    ],
                  ),
                ),
              ),
            );
          },
        ),
      ],
    );
  }
//endregion

//region Edit Detail TextField
  Widget editDetail() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.end,
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        Container(
          //margin: const EdgeInsets.only(bottom: 10),
          padding: const EdgeInsets.all(10),
          decoration: BoxDecoration(
              color: AppColors.textFieldFill1,
              borderRadius: BorderRadius.all(Radius.circular(10))),
          child: Row(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.only(top: 5),
                child: SvgPicture.asset(
                  AppImages.dot,
                  width: 10,
                  height: 10,
                  color: AppColors.appBlack10,
                ),
              ),
              horizontalSizedBox(5),
              Expanded(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    TextFormField(
                      controller: shippingHistoryBloc.titleTextCtrl,
                      textInputAction: TextInputAction.next,
                      keyboardType: TextInputType.name,
                      textCapitalization: TextCapitalization.sentences,
                      style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: AppColors.appBlack,
                          fontFamily: AppConstants.rRegular),
                      decoration: InputDecoration.collapsed(
                          hintText: 'Update title',
                          hintStyle: TextStyle(
                              fontSize: 14,
                              fontFamily: AppConstants.rRegular,
                              fontWeight: FontWeight.w400,
                              color: AppColors.writingColor3)),
                    ),
                    verticalSizedBox(5),
                    TextFormField(
                      controller: shippingHistoryBloc.descTextCtrl,
                      textInputAction: TextInputAction.done,
                      keyboardType: TextInputType.name,
                      maxLines: 3,
                      textCapitalization: TextCapitalization.sentences,
                      style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w400,
                          color: AppColors.appBlack,
                          fontFamily: AppConstants.rRegular),
                      decoration: InputDecoration.collapsed(
                          hintText: 'Add a description & details if any',
                          hintStyle: TextStyle(
                              fontSize: 14,
                              fontFamily: AppConstants.rRegular,
                              fontWeight: FontWeight.w400,
                              color: AppColors.writingColor3)),
                    ),
                    verticalSizedBox(5),
                    appText(shippingHistoryBloc.currentDateTime,
                        fontSize: 12,
                        fontWeight: FontWeight.w400,
                        color: AppColors.appBlack,
                        maxLine: 1,
                        fontFamily: AppConstants.rRegular),
                  ],
                ),
              ),
              //Expanded(child: horizontalSizedBox(10)),
              Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 10, vertical: 3),
                  child: SvgPicture.asset(
                    AppImages.drawerIcon,
                    color: AppColors.appBlack,
                  ))
            ],
          ),
        ),
        verticalSizedBox(15),
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            ///Cancel
            InkWell(
              onTap: () {
                shippingHistoryBloc.titleTextCtrl.clear();
                shippingHistoryBloc.descTextCtrl.clear();
                shippingHistoryBloc.selectedHistoryId = 0;
                shippingHistoryBloc.shippingHistoryCtrl.sink
                    .add(ShippingHistoryState.Success);
              },
              child: Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 50, vertical: 10),
                decoration: BoxDecoration(
                    color: AppColors.lightGray,
                    borderRadius: BorderRadius.all(Radius.circular(10))),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    appText("Cancel",
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: AppColors.writingColor2,
                        maxLine: 5,
                        fontFamily: AppConstants.rRegular),
                  ],
                ),
              ),
            ),
            horizontalSizedBox(10),

            ///Save
            InkWell(
              onTap: () {
                shippingHistoryBloc.onTapSave();
              },
              child: Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 50, vertical: 10),
                decoration: BoxDecoration(
                    color: AppColors.inActiveGreen,
                    borderRadius: BorderRadius.all(Radius.circular(10))),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    appText("Save",
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: AppColors.brandBlack,
                        maxLine: 5,
                        fontFamily: AppConstants.rRegular),
                  ],
                ),
              ),
            ),
          ],
        )
      ],
    );
  }
//endregion

//region Menu button
  Widget historyMenu({required History history}) {
    return PopupMenuButton(
      // shadowColor: Colors.transparent,
      // add icon, by default "3 dot" icon
      // icon: Icon(Icons.book)
      padding: EdgeInsets.zero,
      icon: SvgPicture.asset(AppImages.drawerIcon),
      itemBuilder: (context) {
        return [
          ///Edit
          PopupMenuItem<int>(
            height: 0,
            value: 0,
            onTap: () async {
              await Future.delayed(Duration.zero);
              history.isEditable!
                  ? shippingHistoryBloc.onTapEdit(history: history)
                  : CommonMethods.toastMessage(
                      AppStrings.canNotEditOrDelete, context);
            },
            padding: EdgeInsets.zero,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                    padding: const EdgeInsets.symmetric(
                        vertical: 12, horizontal: 10),
                    child: appText(AppStrings.edit,
                        fontFamily: AppConstants.rRegular,
                        fontSize: 14,
                        fontWeight: FontWeight.w700)),
                divider()
              ],
            ),
          ),

          ///Delete
          PopupMenuItem<int>(
            height: 0,
            value: 0,
            onTap: () async {
              await Future.delayed(Duration.zero);
              history.isEditable!
                  ? shippingHistoryBloc.deleteHistory(
                      historyId: history.shippingUpdateId!)
                  : CommonMethods.toastMessage(
                      AppStrings.canNotEditOrDelete, context);
            },
            padding: EdgeInsets.zero,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Container(
                    padding: const EdgeInsets.symmetric(
                        vertical: 10, horizontal: 10),
                    child: appText(AppStrings.delete,
                        fontFamily: AppConstants.rRegular,
                        fontSize: 14,
                        fontWeight: FontWeight.w700)),
                // divider()
              ],
            ),
          ),
        ];
      },
    );
  }
//endregion
}
