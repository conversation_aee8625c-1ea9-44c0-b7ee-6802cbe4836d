import 'package:flutter/material.dart';
import 'package:mask_text_input_formatter/mask_text_input_formatter.dart';
import 'package:swadesic/features/seller/membership/membership_bloc.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';

class MembershipCommonWidgets{
  //region Membership field
  static Widget membershipCodeField({required MembershipBloc membershipBloc,required BuildContext context,dynamic refreshSetStare}){

    return   TextFormField(
      controller: membershipBloc.referralCodeTextCtrl,
      onTap: (){
        membershipBloc.tabCtrl.sink.add(true);

      },
      keyboardType:membershipBloc.membershipKeyboardType ,

      onChanged: (value){
        // membershipBloc.referralCodeTextCtrl.clear();
        membershipBloc.referralCodeTextCtrl.text = value.toUpperCase();
        membershipBloc.referralCodeTextCtrl.selection = TextSelection(
            baseOffset: membershipBloc.referralCodeTextCtrl.text.length,
            extentOffset: membershipBloc.referralCodeTextCtrl.text.length);

        ///
        if (value.length == 10) {
          FocusScope.of(context).unfocus();
          // myKeyboardType = TextInputType.number;
          refreshSetStare();

          membershipBloc.membershipKeyboardType = TextInputType.number;

          Future.delayed(const Duration(milliseconds: 10), () {
            FocusScope.of(context).requestFocus(membershipBloc.referralFocusNode);
          });
        }



      },
      focusNode: membershipBloc.referralFocusNode,
      textAlign: TextAlign.center,
      style: TextStyle(
        fontSize: 15,
        fontWeight: FontWeight.w600,
        color: AppColors.writingBlack,
         letterSpacing: 4
      ),


      // textCapitalization: TextCapitalization.characters,


      decoration:  InputDecoration(
        hintText:membershipBloc.referralFocusNode.hasFocus?"":"Referral code",
        hintStyle: TextStyle(
          fontSize:15,
          fontWeight: FontWeight.w600,

          color: AppColors.writingBlack.withOpacity(0.4),
          // letterSpacing: 4
        ) ,
        ///Focus border
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
          borderSide: BorderSide(color: AppColors.textFieldFill1, width: 1.5),
        ),
        ///Disable border
        disabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
          borderSide: BorderSide(color: AppColors.textFieldFill1, width: 1.5),
        ),
        ///Enable border
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
          borderSide: BorderSide(color: AppColors.textFieldFill1, width: 1.5),
        ),
      ),
      inputFormatters:[
        // MaskTextInputFormatter(mask: 'A - AAAA - AAAA', filter: { "A": RegExp(r'[A-Z-0-9]') }),
        MaskTextInputFormatter(mask: 'A - AAAA - AAAA', filter: { "A": RegExp(AppConstants.intStringNoSpace) }),

      ],


        // FilteringTextInputFormatter.digitsOnly
    );
  }
  //endregion
}