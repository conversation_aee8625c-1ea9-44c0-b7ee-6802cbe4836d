import 'dart:async';
import 'dart:io';
import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pretty_qr_code/pretty_qr_code.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/common_buyer_seller_screen/share_store_bottom_sheet_screen/share_store_bottom_sheets_bloc.dart';
import 'package:swadesic/features/data_model/logged_in_user_info_data_model/logged_in_user_info_data_model.dart';
import 'package:swadesic/features/data_model/seller_own_store_info_data_model/seller_own_store_info_data_model.dart';
import 'package:swadesic/features/share_with_image/share_with_image_screen.dart';
import 'package:swadesic/features/widgets/custom_image_container/custom_image_container.dart';
import 'package:swadesic/model/store_info/store_info.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_enums.dart';
import 'package:swadesic/util/app_images.dart';
// import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';
// import 'package:url_launcher/url_launcher.dart';

//region Share in social bottom sheet screen Store
class ShareInSocialBottomSheetScreen extends StatefulWidget {
  final StoreInfo storeInfo;
  const ShareInSocialBottomSheetScreen({super.key, required this.storeInfo});

  @override
  State<ShareInSocialBottomSheetScreen> createState() =>
      _ShareInSocialBottomSheetScreenState();
}
//endregion

class _ShareInSocialBottomSheetScreenState
    extends State<ShareInSocialBottomSheetScreen> {
  //region Bloc
  late ShareStoreBottomSheetBloc shareStoreBottomSheetBloc;
  // final GlobalKey _qrCodeKey = GlobalKey(); // Key for QR code capture
  //endregion

  //region Init
  @override
  void initState() {
    shareStoreBottomSheetBloc = ShareStoreBottomSheetBloc(context);
    super.initState();
  }
  //endregion

  @override
  //region Build
  Widget build(BuildContext context) {
    return body();
  }
  //endregion

//region Body
  Widget body() {
    return Container(
      margin: const EdgeInsets.only(top: 40, bottom: 30, left: 20, right: 20),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(20),
            child: SizedBox(
              height: 74,
              width: 75,
              child: extendedImage(
                widget.storeInfo.icon,
                context,
                74,
                74,
                imageHeight: 74,
                imageWidth: 74,
                cache: true,
                customPlaceHolder: AppImages.storePlaceHolder,
              ),
            ),
          ),
          verticalSizedBox(15),
          Text(
            widget.storeInfo.storeName!,
            style: AppTextStyle.usernameHeading(textColor: AppColors.appBlack),
          ),
          verticalSizedBox(30),
          //Toggle button
          toggleButton(),
          //Store link
          StreamBuilder<bool>(
              stream: shareStoreBottomSheetBloc.toggleButtonCtrl.stream,
              builder: (context, snapshot) {
                // Show URL for Store link and Super link, hide for QR Code
                if (shareStoreBottomSheetBloc.isSelected[1]) {
                  return Text(
                    "Scanning the QR Code will open the store",
                    textAlign: TextAlign.center,
                    style:
                        AppTextStyle.access0(textColor: AppColors.brandBlack),
                  );
                }

                return InkWell(
                  onTap: () {
                    CommonMethods.openAppWebView(
                        webUrl:
                            // "${AppConstants.domainName}${widget.storeInfo.storehandle}${shareStoreBottomSheetBloc.isSelected[1] ? "/super_link" : ""}",
                            "${AppConstants.domainName}${widget.storeInfo.storehandle}",
                        context: context);
                  },
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Expanded(
                          child: Text(
                        // "${AppConstants.domainName}${widget.storeInfo.storehandle}${shareStoreBottomSheetBloc.isSelected[1] ? "/super_link" : ""}",
                        "${AppConstants.domainName}${widget.storeInfo.storehandle}",
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1,
                        textAlign: TextAlign.center,
                        style: AppTextStyle.access0(
                            textColor: AppColors.brandBlack),
                      )),
                    ],
                  ),
                );
              }),
          verticalSizedBox(30),
          // Share button
          _buildShareButton(),
        ],
      ),
    );
  }
//endregion

//region Toggle button
  Widget toggleButton() {
    return Container(
      margin: const EdgeInsets.only(bottom: 10),
      child: StreamBuilder<bool>(
          stream: shareStoreBottomSheetBloc.toggleButtonCtrl.stream,
          builder: (context, snapshot) {
            return SizedBox(
              height: 40,
              child: ToggleButtons(
                borderRadius:
                    BorderRadius.circular(30), // Increased for more roundness
                borderWidth: 2,
                borderColor: AppColors.brandBlack,
                selectedBorderColor: AppColors.brandBlack,
                selectedColor: Colors.white,
                fillColor: AppColors.brandBlack,
                isSelected: shareStoreBottomSheetBloc.isSelected,
                onPressed: (int index) {
                  shareStoreBottomSheetBloc.onTapToggle(selectedIndex: index);
                },
                children: [
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 10),
                    child: Text(
                      'Store link',
                      style: AppTextStyle.access0(
                        textColor: shareStoreBottomSheetBloc.isSelected[0]
                            ? Colors.white // Enabled state
                            : Colors.black, // Disabled state
                      ),
                    ),
                  ),
                  // Padding(
                  //   padding: const EdgeInsets.symmetric(horizontal: 10),
                  //   child: Text(
                  //     'Super link',
                  //     style: AppTextStyle.access0(
                  //       textColor: shareStoreBottomSheetBloc.isSelected[1]
                  //           ? Colors.white // Enabled state
                  //           : Colors.black, // Disabled state
                  //     ),
                  //   ),
                  // ),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 10),
                    child: Text(
                      'QR Code',
                      style: AppTextStyle.access0(
                        textColor: shareStoreBottomSheetBloc.isSelected[1]
                            ? Colors.white // Enabled state
                            : Colors.black, // Disabled state
                      ),
                    ),
                  ),
                ],
              ),
            );
          }),
    );
  }
//endregion

  // Build share button (similar to buy button styling)
  Widget _buildShareButton() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      child: ElevatedButton(
        onPressed: () {
          _handleShare();
        },
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.brandBlack,
          foregroundColor: AppColors.appWhite,
          padding: const EdgeInsets.symmetric(vertical: 10),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(60),
          ),
          elevation: 0,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              "Share",
              style: AppTextStyle.access0(textColor: AppColors.appWhite),
            ),
          ],
        ),
      ),
    );
  }

  // Handle share based on selected option
  void _handleShare() async {
    if (shareStoreBottomSheetBloc.isSelected[1]) {
      // QR Code option - generate QR and share as image
      await _shareQRCode();
    } else {
      // Store link - share via ShareWithImageScreen
      await _shareStoreLink();
    }
  }

  // Share QR Code as image
  Future<void> _shareQRCode() async {
    // Generate QR code image
    File? qrCodeImage = await _generateQRCodeImage();

    // Check if widget is still mounted before using context
    if (!mounted) return;

    if (qrCodeImage == null) {
      CommonMethods.toastMessage("Failed to generate QR code image", context);
      return;
    }

    // Create store URL exactly like in share_store_and_profile_screen.dart
    String url = "${AppConstants.domainName}${widget.storeInfo.storehandle}";

    // Close current bottom sheet
    Navigator.of(context).pop();

    // Open ShareWithImageScreen with QR code image
    CommonMethods.accessBottomSheet(
      screen: ShareWithImageScreen(
        url: url,
        imageLink: widget.storeInfo.icon,
        imageType: CustomImageContainerType.store,
        entityType: EntityType.STORE,
        storeName: widget.storeInfo.storeName,
        storeIconUrl: widget.storeInfo.icon,
        objectReference: widget.storeInfo.storeReference,
        message: "Check out my store QR code on Swadesic!",
        attachmentImagePath: qrCodeImage.path,
      ),
      context: context,
    );
  }

  // Share store link via ShareWithImageScreen
  Future<void> _shareStoreLink() async {
    // Create store URL
    String url = "${AppConstants.domainName}${widget.storeInfo.storehandle}";

    // // Add super link if selected
    // if (shareStoreBottomSheetBloc.isSelected[1]) {
    //   url = "$url/super_link";
    // }

    // Add invite code if available
    String inviteCode = _getInviteCode();
    if (inviteCode.isNotEmpty) {
      url = "$url/?ic=$inviteCode";
    }

    // Close current bottom sheet
    Navigator.of(context).pop();

    // Open ShareWithImageScreen
    CommonMethods.accessBottomSheet(
      screen: ShareWithImageScreen(
        url: url,
        imageLink: widget.storeInfo.icon,
        imageType: CustomImageContainerType.store,
        entityType: EntityType.STORE,
        storeName: widget.storeInfo.storeName,
        storeIconUrl: widget.storeInfo.icon,
        objectReference: widget.storeInfo.storeReference,
        // message: shareStoreBottomSheetBloc.isSelected[1]
        //     ? "Check out my store super link on Swadesic!"
        //     : "Check out my store on Swadesic!",
        message: "Check out my store on Swadesic!",
      ),
      context: context,
    );
  }

  // Get invite code based on current user/store view
  String _getInviteCode() {
    if (CommonMethods().isStaticUser()) {
      return "";
    }

    if (AppConstants.appData.isUserView!) {
      LoggedInUserInfoDataModel userDetailDataModel =
          Provider.of<LoggedInUserInfoDataModel>(
              AppConstants.currentSelectedTabContext,
              listen: false);
      return userDetailDataModel.userDetail?.inviteCode ?? "";
    } else {
      SellerOwnStoreInfoDataModel sellerOwnStoreInfoDataModel =
          Provider.of<SellerOwnStoreInfoDataModel>(
              AppConstants.currentSelectedTabContext,
              listen: false);
      return sellerOwnStoreInfoDataModel.storeInfo?.inviteCode ?? "";
    }
  }

  // Generate QR code image using RepaintBoundary to capture actual QR code
  Future<File?> _generateQRCodeImage() async {
    try {
      // Create store URL exactly like in share_store_and_profile_screen.dart
      String url = "${AppConstants.domainName}${widget.storeInfo.storehandle}";

      // Create the same QR code widget as in share_store_and_profile_screen.dart
      final qrWidget = Container(
        width: 320,
        height: 320,
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(24),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.15),
              spreadRadius: 2,
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Stack(
          alignment: Alignment.center,
          children: [
            // QR Code with embedded white square
            PrettyQrView.data(
              data: url,
              errorCorrectLevel: QrErrorCorrectLevel.H,
              decoration: PrettyQrDecoration(
                shape: PrettyQrSmoothSymbol(
                  color: AppColors.appBlack,
                  roundFactor: 1,
                ),
                // Create a white square in the middle of the QR code
                image: PrettyQrDecorationImage(
                  image: AssetImage('assets/common_images/white_square.png'),
                  position: PrettyQrDecorationImagePosition.embedded,
                  scale: 0.3,
                ),
              ),
            ),
            // Custom store icon in the center
            SizedBox(
              width: 70,
              height: 70,
              child: widget.storeInfo.icon != null
                  ? CustomImageContainer(
                      width: 60,
                      height: 60,
                      imageUrl: widget.storeInfo.icon,
                      imageType: CustomImageContainerType.store,
                      showShadow: false,
                    )
                  : Image.asset(
                      'assets/common_images/app_icon_png.png',
                      width: 60,
                      height: 60,
                      fit: BoxFit.cover,
                    ),
            ),
          ],
        ),
      );

      // Use RepaintBoundary to capture the QR code
      final GlobalKey repaintKey = GlobalKey();

      // Create a temporary widget tree to render the QR code
      final qrRepaintWidget = RepaintBoundary(
        key: repaintKey,
        child: qrWidget,
      );

      // Create a temporary overlay to render the widget off-screen
      final overlay = Overlay.of(context);
      late OverlayEntry overlayEntry;

      overlayEntry = OverlayEntry(
        builder: (context) => Positioned(
          left: -1000, // Position off-screen
          top: -1000,
          child: Material(
            color: Colors.transparent,
            child: qrRepaintWidget,
          ),
        ),
      );

      overlay.insert(overlayEntry);

      // Wait for the widget to be rendered and painted
      await Future.delayed(const Duration(milliseconds: 500));

      // Capture the rendered widget
      RenderRepaintBoundary? boundary;
      try {
        // Get the render object directly from the global key
        final renderObject = repaintKey.currentContext?.findRenderObject();
        if (renderObject is RenderRepaintBoundary) {
          boundary = renderObject;
        }
      } catch (e) {
        debugPrint('Error finding render boundary: $e');
      }

      // Remove the overlay
      overlayEntry.remove();

      if (boundary == null) {
        throw Exception('Failed to find render boundary for QR code');
      }

      final ui.Image image = await boundary.toImage(pixelRatio: 2.0);

      // Convert to bytes
      final ByteData? byteData =
          await image.toByteData(format: ui.ImageByteFormat.png);
      if (byteData == null) {
        throw Exception('Failed to convert QR code to bytes');
      }

      final Uint8List pngBytes = byteData.buffer.asUint8List();

      // Get application documents directory
      final directory = await getApplicationDocumentsDirectory();
      final path =
          '${directory.path}/store_qr_code_${DateTime.now().millisecondsSinceEpoch}.png';
      final file = File(path);

      // Save the image file
      await file.writeAsBytes(pngBytes);

      return file;
    } catch (e) {
      debugPrint('Error generating QR code image: $e');
      return null;
    }
  }
}
