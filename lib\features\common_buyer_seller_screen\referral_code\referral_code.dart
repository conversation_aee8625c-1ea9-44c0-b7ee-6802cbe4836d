import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:swadesic/features/common_buyer_seller_screen/referral_code/referral_code_bloc.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/common_widgets.dart';

class ReferralCode extends StatefulWidget {
  const ReferralCode({Key? key}) : super(key: key);

  @override
  State<ReferralCode> createState() => _ReferralCodeState();
}

class _ReferralCodeState extends State<ReferralCode> {
  //region Bloc
  late ReferralCodeBloc referralCodeBloc;

  //endregion
  //region Init
  @override
  void initState() {
    referralCodeBloc = ReferralCodeBloc(context);
    referralCodeBloc.init();
    super.initState();
  }

  //endregion

  //region Dispose
  @override
  void dispose() {
    referralCodeBloc.dispose();
    super.dispose();
  }

  //endregion
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
        onTap: () {
          FocusScope.of(context).unfocus();
        },
        child: body());
  }

  //region Body
  Widget body() {
    return SizedBox(
      height: 63,
      child: RawKeyboardListener(
        focusNode: FocusNode(),
        onKey: (value) {
          if (value.isKeyPressed(LogicalKeyboardKey.backspace) &&
              ReferralCodeBloc.thirdFieldTextCtrl.text.isEmpty &&
              referralCodeBloc.thirdFocusNode!.hasFocus) {
            // Remove one last char from second second text controller
            List<String> data =
                ReferralCodeBloc.secondFieldTextCtrl.text.split("");
            data.removeLast();
            ReferralCodeBloc.secondFieldTextCtrl.text = data.join();
            return FocusScope.of(context)
                .requestFocus(referralCodeBloc.secondFocusNode);
          }

          if (value.isKeyPressed(LogicalKeyboardKey.backspace) &&
              ReferralCodeBloc.secondFieldTextCtrl.text.isEmpty) {
            return FocusScope.of(context)
                .requestFocus(referralCodeBloc.firstFocusNode);
          }

          // if(value.isKeyPressed(LogicalKeyboardKey.backspace) && ReferralCodeBloc.thirdFieldTextCtrl.text.isEmpty && referralCodeBloc.thirdFocusNode!.hasFocus){
          //   //If second field already is not empty
          //   if(ReferralCodeBloc.secondFieldTextCtrl.text.isNotEmpty){
          //     //Remove one last char from second second text controller
          //     List<String> data = ReferralCodeBloc.secondFieldTextCtrl.text.split("");
          //     data.removeLast();
          //     ReferralCodeBloc.secondFieldTextCtrl.text = data.join();
          //     //Focus to the second field
          //     return FocusScope.of(context).requestFocus(referralCodeBloc.secondFocusNode);
          //   }
          //   else{
          //     return;
          //   }
          //
          // }

          // if(value.isKeyPressed(LogicalKeyboardKey.backspace)&& ReferralCodeBloc.secondFieldTextCtrl.text.isEmpty && referralCodeBloc.secondFocusNode!.hasFocus){
          //   FocusScope.of(context).requestFocus(referralCodeBloc.firstFocusNode);
          // }
          //print(value);
        },
        child: Scaffold(
          backgroundColor: AppColors.appWhite,
          body: Center(
            child: Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Row(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    ///Field 1
                    SizedBox(
                      // margin: const EdgeInsets.only(left: 20,right: 5),
                      // flex: 1,
                      // margin: const EdgeInsets.symmetric(horizontal: 10),
                      width: 100,

                      child: InkWell(
                        onTap: () {
                          if (ReferralCodeBloc.firstFieldTextCtrl.text.length ==
                              1) {
                            FocusScope.of(context)
                                .requestFocus(referralCodeBloc.secondFocusNode);
                          } else {
                            FocusScope.of(context)
                                .requestFocus(referralCodeBloc.firstFocusNode);
                          }
                        },
                        onLongPress: () {
                          referralCodeBloc.pasteDialog();
                        },
                        child: AbsorbPointer(
                          child: TextFormField(
                            // enableInteractiveSelection: false,

                            controller: ReferralCodeBloc.firstFieldTextCtrl,
                            focusNode: referralCodeBloc.firstFocusNode,
                            textAlign: TextAlign.center,
                            onChanged: (value) {
                              referralCodeBloc.onChangeFirstTextFieldChange(
                                  value: value);
                            },
                            //onTap: (){
                            ///If text field is empty jump focus to previous field
                            // if (ReferralCodeBloc.secondFieldTextCtrl.text.isEmpty) {
                            //   FocusScope.of(context).requestFocus(referralCodeBloc.firstFocusNode);
                            // }
                            ////If text field length is 1
                            // if(ReferralCodeBloc.firstFieldTextCtrl.text.length == 1){
                            //   FocusScope.of(context).requestFocus(referralCodeBloc.secondFocusNode);
                            // }
                            ///If next field leangth is 4 then jump further
                            // if(ReferralCodeBloc.secondFieldTextCtrl.text.length == 4){
                            //   FocusScope.of(context).requestFocus(referralCodeBloc.thirdFocusNode);
                            // }
                            //},
                            style: AppCommonWidgets.referralTextStyle(),
                            textCapitalization: TextCapitalization.characters,
                            decoration: referralDecoration(dots: "•"),

                            inputFormatters: [
                              LengthLimitingTextInputFormatter(1),
                              FilteringTextInputFormatter.allow(
                                  RegExp(AppConstants.onlyStringNoSpace)),
                            ],
                          ),
                        ),
                      ),
                    ),
                    // dashToSplit(),
                    ///Field 2
                    Container(
                      // flex: 2,
                      margin: const EdgeInsets.symmetric(horizontal: 15),
                      alignment: Alignment.center,
                      width: 100,
                      // flex: 2,
                      child: InkWell(
                        onTap: () {
                          if (ReferralCodeBloc
                                  .secondFieldTextCtrl.text.isEmpty &&
                              ReferralCodeBloc
                                  .firstFieldTextCtrl.text.isEmpty) {
                            FocusScope.of(context)
                                .requestFocus(referralCodeBloc.firstFocusNode);
                          } else {
                            FocusScope.of(context)
                                .requestFocus(referralCodeBloc.secondFocusNode);
                          }
                        },
                        onLongPress: () {
                          referralCodeBloc.pasteDialog();
                        },
                        child: AbsorbPointer(
                          child: TextFormField(
                            controller: ReferralCodeBloc.secondFieldTextCtrl,
                            focusNode: referralCodeBloc.secondFocusNode,
                            textAlign: TextAlign.center,
                            onChanged: (value) {
                              referralCodeBloc.onChangeSecondTextFieldChange(
                                  value: value);
                            },
                            // onTap: () {
                            ////If text field is empty jump focus to previous field
                            // if (ReferralCodeBloc.secondFieldTextCtrl.text.isEmpty && ReferralCodeBloc.firstFieldTextCtrl.text.isEmpty) {
                            //   FocusScope.of(context).requestFocus(referralCodeBloc.firstFocusNode);
                            // }
                            ///If text field length is 4
                            // if(ReferralCodeBloc.secondFieldTextCtrl.text.length == 4){
                            //   FocusScope.of(context).requestFocus(referralCodeBloc.thirdFocusNode);
                            // }
                            // },
                            style: AppCommonWidgets.referralTextStyle(),
                            textCapitalization: TextCapitalization.characters,
                            decoration: referralDecoration(),
                            inputFormatters: [
                              LengthLimitingTextInputFormatter(4),
                              FilteringTextInputFormatter.allow(
                                RegExp(AppConstants.onlyStringNoSpace),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                    // dashToSplit(),
                    ///Field 3
                    SizedBox(
                      // flex: 2,
                      // alignment: Alignment.center,
                      width: 100,
                      // margin: const EdgeInsets.symmetric(horizontal: 5),
                      // width: 80,

                      // flex: 2,
                      child: InkWell(
                        onTap: () {
                          if (ReferralCodeBloc
                              .secondFieldTextCtrl.text.isEmpty) {
                            FocusScope.of(context)
                                .requestFocus(referralCodeBloc.secondFocusNode);
                          } else {
                            FocusScope.of(context)
                                .requestFocus(referralCodeBloc.thirdFocusNode);
                          }
                        },
                        onLongPress: () {
                          referralCodeBloc.pasteDialog();
                        },
                        child: AbsorbPointer(
                          child: TextFormField(
                            controller: ReferralCodeBloc.thirdFieldTextCtrl,
                            focusNode: referralCodeBloc.thirdFocusNode,
                            textAlign: TextAlign.center,
                            keyboardType: TextInputType.number,
                            onChanged: (value) {
                              referralCodeBloc.onChangeThirdTextFieldChange(
                                  value: value);
                            },
                            // onTap: () {
                            ////If previous is empty
                            // if (ReferralCodeBloc.secondFieldTextCtrl.text.isEmpty) {
                            //   FocusScope.of(context).requestFocus(referralCodeBloc.secondFocusNode);
                            // }
                            // if (ReferralCodeBloc.thirdFieldTextCtrl.text.isEmpty ) {
                            //   FocusScope.of(context).requestFocus(referralCodeBloc.secondFocusNode);
                            // }
                            // if (ReferralCodeBloc.thirdFieldTextCtrl.text.isEmpty && ReferralCodeBloc.secondFieldTextCtrl.text.isEmpty) {
                            //   FocusScope.of(context).requestFocus(referralCodeBloc.firstFocusNode);
                            // }
                            // },
                            style: AppCommonWidgets.referralTextStyle(),
                            textCapitalization: TextCapitalization.characters,
                            decoration: referralDecoration(),
                            inputFormatters: [
                              FilteringTextInputFormatter.allow(
                                  RegExp(AppConstants.onlyInt)),
                              FilteringTextInputFormatter.digitsOnly,
                              LengthLimitingTextInputFormatter(4),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                // Expanded(child: SizedBox())
              ],
            ),
          ),
        ),
      ),
    );
  }
//endregion

  //region Field style
  //endre

//region Input decoration
  InputDecoration referralDecoration({String dots = "••••"}) {
    return InputDecoration(
      contentPadding: const EdgeInsets.symmetric(vertical: 15),
      isDense: true,
      hintText: dots,
      hintStyle: TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.w600,
          color: AppColors.writingColor4,
          letterSpacing: 4),
      border: InputBorder.none,

      ///Focus border
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(10),
        borderSide: BorderSide(color: AppColors.textFieldFill1, width: 1.5),
      ),

      ///Disable border
      disabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(10),
        borderSide: BorderSide(color: AppColors.textFieldFill1, width: 1.5),
      ),

      ///Enable border
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(10),
        borderSide: BorderSide(color: AppColors.textFieldFill1, width: 1.5),
      ),
    );
  }
//endregion

//region Dash to split
  Widget dashToSplit() {
    return Text(
      "-",
      style: TextStyle(
        fontSize: 20,
        fontWeight: FontWeight.w600,
        color: AppColors.writingColor4,
      ),
    );
  }
//endregion
}
