class GetNotificationResponse {
  String? message;
  int notSeenCount = 0;
  List<NotificationDetail>? notifications;

  GetNotificationResponse(
      {this.message, this.notifications});

  GetNotificationResponse.fromJson(Map<String, dynamic> json) {
    message = json['message'];
    notSeenCount = json['not_seen_count']??0;
    if (json['notifications'] != null) {
      notifications = <NotificationDetail>[];
      json['notifications'].forEach((v) {
        notifications!.add(new NotificationDetail.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['message'] = this.message;
    data['not_seen_count'] = this.notSeenCount;
    if (this.notifications != null) {
      data['notifications'] =
          this.notifications!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class NotificationDetail {
  String? notificationReference;
  String? notifiedUser;
  String? notificationType;
  String? notificationMessage;
  String? notificationStatus;
  String? notificationAbout;
  String? date;
  String? image;
  String? time;
  String? group;
  String? actionPage;

  NotificationDetail(
      {this.notificationReference,
        this.notifiedUser,
        this.notificationType,
        this.notificationMessage,
        this.notificationStatus,
        this.notificationAbout,
        this.date,
        this.image,
        this.time,
        this.actionPage,
        this.group});

  NotificationDetail.fromJson(Map<String, dynamic> json) {
    notificationReference = json['notification_reference'];
    notifiedUser = json['notified_user'];
    notificationType = json['notification_type'];
    notificationMessage = json['notification_message'];
    notificationStatus = json['notification_status'];
    notificationAbout = json['notification_about'];
    date = json['date'];
    image = json['image'];
    time = json['time'];
    group = json['group'];
    actionPage = json['action_page'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['notification_reference'] = this.notificationReference;
    data['notified_user'] = this.notifiedUser;
    data['notification_type'] = this.notificationType;
    data['notification_message'] = this.notificationMessage;
    data['notification_status'] = this.notificationStatus;
    data['notification_about'] = this.notificationAbout;
    data['date'] = this.date;
    data['image'] = this.image;
    data['time'] = this.time;
    data['group'] = this.group;
    data['action_page'] = this.actionPage;
    return data;
  }
}
