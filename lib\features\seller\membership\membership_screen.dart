import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/buyers/buyer_home/buyer_home_bloc.dart';
import 'package:swadesic/features/common_buyer_seller_screen/referral_code/referral_code.dart';
import 'package:swadesic/features/seller/membership/friends_and_invites.dart';
import 'package:swadesic/features/seller/membership/membership_bloc.dart';
import 'package:swadesic/features/seller/membership/sent.dart';
import 'package:swadesic/features/seller/membership/share_app.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

class MembershipScreen extends StatefulWidget {
  const MembershipScreen({Key? key}) : super(key: key);

  @override
  _MembershipScreenState createState() => _MembershipScreenState();
}

class _MembershipScreenState extends State<MembershipScreen>
    with TickerProviderStateMixin {
  //region Bloc
  late MembershipBloc membershipBloc;

  //endregion

  //region Init
  @override
  void initState() {
    membershipBloc = MembershipBloc(context);
    membershipBloc.init();
    super.initState();
  }

  //endregion

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        CommonMethods.closeKeyboard(context);
        membershipBloc.isBottomSheetVisible = false;
        membershipBloc.inviteCtrl.sink.add(true);
        membershipBloc.isVisiblePhoneSheetCtrl.sink.add(false);
        membershipBloc.isAlreadyJoinedVisible = false;
        membershipBloc.alreadyJoinedCtrl.sink.add(true);
      },
      child: Scaffold(
          appBar: appBar(), backgroundColor: AppColors.appWhite, body: body()),
    );
  }

  //region AppBar
  AppBar appBar() {
    return AppCommonWidgets.mainAppBar(
        context: context,
        isTitleVisible: true,
        isCustomTitle: false,
        title: AppStrings.membership,
        isDefaultMenuVisible: true,
        isCartVisible: true,
        isMembershipVisible: false,
        onTapDrawer: () {
          // buyerViewStoreBloc.goToSellerAccountScreen();
        });
  }

  //endregion

//region Body
  Widget body() {
    return Stack(
      children: [
        Column(
          children: [
            tabBar(),
            Expanded(
                child: Stack(
              children: [
                tabView(),

                ///Send and request bottom sheet
                StreamBuilder<bool>(
                    stream: membershipBloc.inviteCtrl.stream,
                    initialData: membershipBloc.isBottomSheetVisible,
                    builder: (context, snapshot) {
                      return Visibility(
                          visible: membershipBloc.isBottomSheetVisible,
                          child: bottomSheet(
                              membershipBloc.sendAndRequest,
                              membershipBloc.selectedFriendRank,
                              membershipBloc.selectedFriendMobileNumber,
                              membershipBloc.selectedFriendName));
                    }),

                ///Send using mobile number bottom sheet
                StreamBuilder<bool>(
                    stream: membershipBloc.isVisiblePhoneSheetCtrl.stream,
                    initialData: false,
                    builder: (context, snapshot) {
                      return Visibility(
                          visible: snapshot.data!, child: sendUsingPhone());
                    }),

                ///Already joined bottom sheet
                StreamBuilder<bool>(
                    stream: membershipBloc.alreadyJoinedCtrl.stream,
                    initialData: false,
                    builder: (context, snapshot) {
                      return Visibility(
                          visible: membershipBloc.isAlreadyJoinedVisible,
                          child: alreadySend());
                    })
              ],
            )),
          ],
        ),
        StreamBuilder<bool>(
            stream: membershipBloc.filterCtrl.stream,
            initialData: true,
            builder: (context, snapshot) {
              return Visibility(
                  visible: membershipBloc.isFilterVisible, child: filter());
            })
      ],
    );
  }

//endregion

  //region My Membership, Friends & Invites and Sent
  Widget tabBar() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
      child: StreamBuilder<bool>(
          stream: membershipBloc.tabCtrl.stream,
          builder: (context, snapshot) {
            return Row(
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                //Membership
                CupertinoButton(
                  padding: EdgeInsets.zero,
                  onPressed: () {
                    membershipBloc.onSelectTab(0);
                  },
                  child: Text(
                    "My membership",
                    style: TextStyle(
                      fontFamily: "LatoSemibold",
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: membershipBloc.selectedTab == 0
                          ? AppColors.writingColor2
                          : AppColors.writingColor3,
                    ),
                  ),
                ),
                //Friends
                CupertinoButton(
                  padding: EdgeInsets.zero,
                  onPressed: () {
                    membershipBloc.onSelectTab(1);
                    //print("friend tab");
                  },
                  child: Text(
                    "Friends & Invites",
                    style: TextStyle(
                      fontFamily: "LatoSemibold",
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: membershipBloc.selectedTab == 1
                          ? AppColors.writingColor2
                          : AppColors.writingColor3,
                    ),
                  ),
                ),
                //Sent
                CupertinoButton(
                  padding: EdgeInsets.zero,
                  onPressed: () {
                    membershipBloc.onSelectTab(2);
                  },
                  child: Text(
                    "Sent",
                    style: TextStyle(
                      fontFamily: "LatoSemibold",
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: membershipBloc.selectedTab == 2
                          ? AppColors.writingColor2
                          : AppColors.writingColor3,
                    ),
                  ),
                ),
              ],
            );
          }),
    );
  }

  //endregion

  //region TabView
  Widget tabView() {
    //print(membershipBloc.selectedTab);
    return StreamBuilder<bool>(
        stream: membershipBloc.tabCtrl.stream,
        initialData: true,
        builder: (context, snapshot) {
          return IndexedStack(
            index: membershipBloc.selectedTab,
            children: [
              myMembership(),
              FriendsAndInvites(
                membershipBloc: membershipBloc,
              ),
              SentInvites(
                membershipBloc: membershipBloc,
              )
            ],
          );

          //
          // if(membershipBloc.selectedTab == "My membership"){
          //   return myMembership();
          // }
          // if(membershipBloc.selectedTab == "Friends & Invites"){
          //   return FriendsAndInvites(membershipBloc: membershipBloc,);
          // }
          // return SentInvites(membershipBloc: membershipBloc,);
        });
  }

  //endregion

  ///My Membership
  //region My Membership
  //region My Member
  Widget myMembership() {
    //Role
    String role = "";
    switch (BuyerHomeBloc.userDetailsResponse.userDetail!.role) {
      case "NON_MEMBER":
        role = "not a member.";
        break;
      case "MEMBER":
        role = "a member.";
        break;
      default:
        role = "a seller";
    }

    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.only(left: 10, right: 10),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.center,
          // shrinkWrap: true,
          // padding: EdgeInsets.symmetric(horizontal: 15),
          children: [
            Container(
              alignment: Alignment.centerLeft,
              child: Text(
                "You are $role",
                style: TextStyle(
                  fontFamily: "LatoSemibold",
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: AppColors.appBlack,
                ),
              ),
            ),
            Visibility(
              visible: BuyerHomeBloc.userDetailsResponse.userDetail!.role ==
                      "NON_MEMBER" ||
                  BuyerHomeBloc.userDetailsResponse.userDetail!.role ==
                      "MEMBER",
              child: Column(
                children: [
                  verticalSizedBox(40),
                  Text(
                    "Enter an invite code to become a member or seller",
                    style: TextStyle(
                      fontFamily: "LatoSemibold",
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: AppColors.writingColor2,
                    ),
                  ),
                  verticalSizedBox(20),
                  //Referral code field
                  StreamBuilder<bool>(
                      stream: membershipBloc.membershipFieldCtrl.stream,
                      builder: (context, snapshot) {
                        return const Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            // Container(
                            //   width: 250,
                            //   child: TextFormField(
                            //     controller: membershipBloc.referralCodeTextCtrl,
                            //     onTap: () {
                            //       membershipBloc.tabCtrl.sink.add(true);
                            //     },
                            //     keyboardType: membershipBloc.membershipKeyboardType,
                            //
                            //     onChanged: (value) {
                            //       // membershipBloc.referralCodeTextCtrl.clear();
                            //       membershipBloc.referralCodeTextCtrl.text = membershipBloc.referralCodeTextCtrl.text.toUpperCase();
                            //       membershipBloc.referralCodeTextCtrl.selection = TextSelection(
                            //           baseOffset: membershipBloc.referralCodeTextCtrl.text.length,
                            //           extentOffset: membershipBloc.referralCodeTextCtrl.text.length);
                            //
                            //       ///Change keyboard
                            //       //print(value.length);
                            //
                            //       ///Close keyboard
                            //       if (value.length == 11) {
                            //         CommonMethods.closeKeyboard(context);
                            //       }
                            //
                            //       ///If text field is empty
                            //       if (value.isEmpty) {
                            //         setState(() {
                            //           FocusScope.of(context).unfocus();
                            //           membershipBloc.membershipKeyboardType = TextInputType.text;
                            //         });
                            //         membershipBloc.membershipFieldCtrl.sink.add(true);
                            //         Future.delayed(const Duration(milliseconds: 100), () {
                            //           FocusScope.of(context).requestFocus(membershipBloc.referralFocusNode);
                            //         });
                            //       }
                            //
                            //       if (value.length == 7) {
                            //         setState(() {
                            //           FocusScope.of(context).unfocus();
                            //           membershipBloc.membershipKeyboardType = TextInputType.number;
                            //         });
                            //         membershipBloc.membershipFieldCtrl.sink.add(true);
                            //         Future.delayed(const Duration(milliseconds: 100), () {
                            //           FocusScope.of(context).requestFocus(membershipBloc.referralFocusNode);
                            //         });
                            //       }
                            //       if (value.length < 7 && membershipBloc.membershipKeyboardType == TextInputType.number) {
                            //         setState(() {
                            //           FocusScope.of(context).unfocus();
                            //           membershipBloc.membershipKeyboardType = TextInputType.text;
                            //         });
                            //         membershipBloc.membershipFieldCtrl.sink.add(true);
                            //         Future.delayed(const Duration(milliseconds: 100), () {
                            //           FocusScope.of(context).requestFocus(membershipBloc.referralFocusNode);
                            //         });
                            //       }
                            //       //Smaller then 8 it will always be text
                            //       // if(value.length < 7){
                            //       //   membershipBloc.membershipKeyboardType = TextInputType.text;
                            //       // }
                            //     },
                            //     focusNode: membershipBloc.referralFocusNode,
                            //     textAlign: TextAlign.center,
                            //     style: const TextStyle(fontSize: 20, fontWeight: FontWeight.w600, color: AppColors.writingBlack, letterSpacing: 5),
                            //
                            //     // textCapitalization: TextCapitalization.characters,
                            //
                            //     decoration: InputDecoration(
                            //       hintText: membershipBloc.referralFocusNode.hasFocus ? "" : "• - •••• - ••••",
                            //       hintStyle: const TextStyle(
                            //         fontSize: 20,
                            //         fontWeight: FontWeight.w600,
                            //
                            //         color: AppColors.writingColor4,
                            //         // letterSpacing: 4
                            //       ),
                            //
                            //       ///Focus border
                            //       focusedBorder: OutlineInputBorder(
                            //         borderRadius: BorderRadius.circular(10),
                            //         borderSide: const BorderSide(color: AppColors.lightestGrey, width: 1.5),
                            //       ),
                            //
                            //       ///Disable border
                            //       disabledBorder: OutlineInputBorder(
                            //         borderRadius: BorderRadius.circular(10),
                            //         borderSide: const BorderSide(color: AppColors.lightestGrey, width: 1.5),
                            //       ),
                            //
                            //       ///Enable border
                            //       enabledBorder: OutlineInputBorder(
                            //         borderRadius: BorderRadius.circular(10),
                            //         borderSide: BorderSide(color: AppColors.lightestGrey, width: 1.5),
                            //       ),
                            //     ),
                            //     inputFormatters: [
                            //       ReferralFormat(),
                            //
                            //       // MaskTextInputFormatter(mask: 'A - AAAA - AAAA', filter: { "A": RegExp(r'[A-Z-0-9]') }),
                            //       // MaskTextInputFormatter(mask: 'A - AAAA - AAAA', filter: { "A": RegExp(AppConstants.intStringNoSpace) }),
                            //       LengthLimitingTextInputFormatter(11)
                            //       // MaskTextInputFormatter(
                            //       //     mask: 'A - AAAA - AAAA',
                            //       //     filter: { "A": RegExp(AppConstants.intStringNoSpace) },
                            //       //     type: MaskAutoCompletionType.lazy
                            //       // )
                            //     ],
                            //
                            //     // FilteringTextInputFormatter.digitsOnly
                            //   ),
                            // ),
                            ReferralCode()
                          ],
                        );
                      }),
                  verticalSizedBox(20),
                  //Submit
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 70),
                    child: InkWell(
                      onTap: () {
                        //print(ClipboardStatus.notPasteable);
                        //membershipBloc.getReferralCode();
                      },
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 10,
                          vertical: 15,
                        ),
                        decoration: BoxDecoration(
                            color: AppColors.brandBlack,
                            borderRadius:
                                BorderRadius.all(Radius.circular(50))),
                        child: Center(
                          child: Text(
                            "Submit",
                            style: TextStyle(
                                fontFamily: "LatoBold",
                                color: AppColors.appWhite,
                                fontSize: 15,
                                fontWeight: FontWeight.w700),
                          ),
                        ),
                      ),
                    ),
                  ),
                  InkWell(
                    onTap: () {
                      membershipBloc.onSelectTab(1);
                    },
                    child: Container(
                      padding: const EdgeInsets.only(bottom: 30, top: 20),
                      child: Text(
                        "See who of your friends are members",
                        style: TextStyle(
                          fontFamily: "LatoBold",
                          fontSize: 15,
                          fontWeight: FontWeight.w700,
                          color: AppColors.brandBlack.withOpacity(0.6),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            //help
            help(),
          ],
        ),
      ),
    );
  }

  //endregion

  //region Help
  Widget help() {
    return Column(
      children: [
        Container(
          alignment: Alignment.centerLeft,
          child: Text(
            "Help",
            style: TextStyle(
              fontFamily: "LatoSemibold",
              fontSize: 15,
              fontWeight: FontWeight.w600,
              color: AppColors.writingColor2,
            ),
          ),
        ),
        verticalSizedBox(10),
        Padding(
          padding: const EdgeInsets.only(top: 15),
          child: commonTooltip("Why Swadesic is member only platform?"),
        ),
        Padding(
          padding: const EdgeInsets.only(top: 15),
          child: commonTooltip("How to become a seller with/ without invites?"),
        ),
        Padding(
          padding: const EdgeInsets.only(top: 15),
          child: commonTooltip("How to become a member?"),
        ),
        Padding(
          padding: const EdgeInsets.only(top: 15),
          child: commonTooltip("How can I buy products as a non-member?"),
        ),
      ],
    );
  }

  //endregion
  //endregion

  //region Send invite using phone bottom sheet
  Widget sendUsingPhone() {
    return Positioned(
      bottom: 0,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 10),
        width: MediaQuery.of(context).size.width,
        decoration: BoxDecoration(
            color: AppColors.appWhite,
            border: Border.all(
              color: AppColors.lightStroke,
            ),
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(25),
              topRight: Radius.circular(25),
            )),
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              //Send invite using mobile number
              Container(
                width: double.infinity,
                alignment: Alignment.centerLeft,
                child: Padding(
                  padding: const EdgeInsets.only(left: 10),
                  child: Text(
                    "Send invite to phone number",
                    style: TextStyle(
                      fontFamily: "LatoSemibold",
                      fontSize: 15,
                      fontWeight: FontWeight.w600,
                      color: AppColors.writingColor2,
                    ),
                  ),
                ),
              ),
              verticalSizedBox(20),
              //Mobile Name
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: colorFilledTextField(
                  onTapTextField: () {
                    membershipBloc.mobileNumberSheetCtrl.sink
                        .add(MembershipState.Empty);
                  },
                  context: context,
                  textFieldCtrl: membershipBloc.nameTextCtrl,
                  hintText: "Name",
                  // isRefCodeFormat: true,

                  textFieldMaxLine: 1,
                  textAlign: TextAlign.start,
                  keyboardType: TextInputType.text,
                  textInputAction: TextInputAction.done,
                  regExp: AppConstants.acceptAll,
                  fieldTextCapitalization: TextCapitalization.characters,
                  fillColor: AppColors.appWhite,
                  // letterSpacing:0,
                  maxCharacter: 10,
                  // styleFontSize: 15,
                  // hintFontSize: 15,
                  contentPaddingVertical: 15,
                  // textColor: AppColors.appBlack8,
                  // textFontWeight: FontWeight.w600
                ),
              ),
              verticalSizedBox(20),
              //Mobile number
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: colorFilledTextField(
                  onTapTextField: () {
                    membershipBloc.mobileNumberSheetCtrl.sink
                        .add(MembershipState.Empty);
                  },
                  context: context,
                  textFieldCtrl: membershipBloc.mobileNumberTextCtrl,
                  hintText: "Phone number",
                  // isRefCodeFormat: true,

                  textFieldMaxLine: 1,
                  textAlign: TextAlign.start,
                  keyboardType: TextInputType.number,
                  textInputAction: TextInputAction.done,
                  regExp: AppConstants.acceptAll,
                  fieldTextCapitalization: TextCapitalization.characters,
                  fillColor: AppColors.appWhite,
                  // letterSpacing:0,
                  maxCharacter: 10,
                  // styleFontSize: 15,
                  // hintFontSize: 15,
                  contentPaddingVertical: 15,
                  // textColor: AppColors.appBlack8,
                  // textFontWeight: FontWeight.w600
                ),
              ),
              verticalSizedBox(20),

              StreamBuilder<MembershipState>(
                  stream: membershipBloc.mobileNumberSheetCtrl.stream,
                  initialData: MembershipState.Empty,
                  builder: (context, snapshot) {
                    if (snapshot.data == MembershipState.Success) {
                      /// Send option 1 -> Send member invite, 2 -> Send seller invite
                      int sendOption = 0;

                      ///If user rank is 3 and friend rank is 1 then send option = 3
                      if (AppConstants.userMemberRank == 3 &&
                          membershipBloc.numberAccessResponse.friendsInfoList!
                                  .first.rank ==
                              1) {
                        sendOption = 3;
                      }

                      ///If user rank is 3 and friend rank is 2 then send option = 2
                      if (AppConstants.userMemberRank == 3 &&
                          membershipBloc.numberAccessResponse.friendsInfoList!
                                  .first.rank ==
                              2) {
                        sendOption = 2;
                      }

                      ///If user rank is 2 and friend rank is 1 then send option = 2
                      if (AppConstants.userMemberRank == 2 &&
                          membershipBloc.numberAccessResponse.friendsInfoList!
                                  .first.rank ==
                              1) {
                        sendOption = 1;
                      }
                      // //print(sendOption);

                      //Send member and seller invite
                      if (sendOption == 1) {
                        return Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 20),
                          child: Column(
                            children: [
                              theNumberIsCurrently(),
                              verticalSizedBox(20),
                              sendMemberInvite(
                                  membershipBloc.numberAccessResponse
                                      .friendsInfoList!.first.phonenumber!,
                                  membershipBloc.numberAccessResponse
                                      .friendsInfoList!.first.name),
                            ],
                          ),
                        );
                      }
                      //Send member and seller invite
                      if (sendOption == 2) {
                        return Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 20),
                          child: Column(
                            children: [
                              theNumberIsCurrently(),
                              verticalSizedBox(10),
                              sendSellerInvite(
                                  membershipBloc.numberAccessResponse
                                      .friendsInfoList!.first.phonenumber!,
                                  membershipBloc.numberAccessResponse
                                      .friendsInfoList!.first.name),
                              const ShareMembershipAppButton(),
                            ],
                          ),
                        );
                      }
                      if (sendOption == 3) {
                        return Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 20),
                          child: Column(
                            children: [
                              theNumberIsCurrently(),
                              verticalSizedBox(20),
                              sendMemberInvite(
                                  membershipBloc.numberAccessResponse
                                      .friendsInfoList!.first.phonenumber!,
                                  membershipBloc.numberAccessResponse
                                      .friendsInfoList!.first.name),
                              verticalSizedBox(10),
                              sendSellerInvite(
                                  membershipBloc.numberAccessResponse
                                      .friendsInfoList!.first.phonenumber!,
                                  membershipBloc.numberAccessResponse
                                      .friendsInfoList!.first.name),
                              const ShareMembershipAppButton(),
                            ],
                          ),
                        );
                      }

                      return Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 20),
                        child: Column(
                          children: [
                            theNumberIsCurrently(),
                            verticalSizedBox(20),
                          ],
                        ),
                      );
                    }

                    return Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        //Check number
                        CupertinoButton(
                          padding: EdgeInsets.zero,
                          onPressed: () {
                            //Check number
                            if (membershipBloc
                                    .mobileNumberTextCtrl.text.length !=
                                10) {
                              CommonMethods.toastMessage(
                                  AppStrings.enterValidNumber, context);
                              return;
                            }
                            membershipBloc.getFriendsInviteApiCall([
                              "+91${membershipBloc.mobileNumberTextCtrl.text}"
                            ]);
                            membershipBloc.selectedFriendName =
                                membershipBloc.nameTextCtrl.text;
                          },
                          child: Container(
                              decoration: BoxDecoration(
                                  color: AppColors.inActiveGreen,
                                  borderRadius:
                                      BorderRadius.all(Radius.circular(20))),
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 40, vertical: 10),
                              child: Text(
                                "Check number",
                                style: TextStyle(
                                  fontFamily: "LatoBold",
                                  fontSize: 15,
                                  fontWeight: FontWeight.w600,
                                  color: AppColors.brandBlack,
                                ),
                              )),
                        ),
                      ],
                    );
                  })
            ],
          ),
        ),
      ),
    );
  }

  //endregion

  //region The number is currently
  Widget theNumberIsCurrently() {
    return StreamBuilder<MembershipState>(
        stream: membershipBloc.mobileNumberSheetCtrl.stream,
        initialData: MembershipState.Empty,
        builder: (context, snapshot) {
          return Container(
            padding: const EdgeInsets.symmetric(horizontal: 10),
            width: double.infinity,
            alignment: Alignment.centerLeft,
            child: Padding(
              padding: const EdgeInsets.only(left: 10),
              child: membershipBloc
                          .numberAccessResponse.friendsInfoList!.first.rank ==
                      1
                  ? Text(
                      "The number is currently a non-member",
                      style: TextStyle(
                        fontFamily: "LatoSemibold",
                        fontSize: 15,
                        fontWeight: FontWeight.w600,
                        color: AppColors.writingColor2,
                      ),
                    )
                  : membershipBloc.numberAccessResponse.friendsInfoList!.first
                              .rank ==
                          2
                      ? Text(
                          "The number is currently a member",
                          style: TextStyle(
                            fontFamily: "LatoSemibold",
                            fontSize: 15,
                            fontWeight: FontWeight.w600,
                            color: AppColors.writingColor2,
                          ),
                        )
                      : Text(
                          "The number is currently a seller",
                          style: TextStyle(
                            fontFamily: "LatoSemibold",
                            fontSize: 15,
                            fontWeight: FontWeight.w600,
                            color: AppColors.writingColor2,
                          ),
                        ),
            ),
          );
        });
  }

  //endregion

  //region Bottom sheet
  Widget bottomSheet(
      String sendRequest, int friendRank, String phoneNumber, String name) {
    if (sendRequest == "send") {
      /// Send option 1 -> Send member invite, 2 -> Send seller invite
      int sendOption = 0;

      ///If user rank is 3 and friend rank is 1 then send option = 3
      if (AppConstants.userMemberRank == 3 && friendRank == 1) {
        sendOption = 3;
      }

      ///If user rank is 3 and friend rank is 2 then send option = 2
      if (AppConstants.userMemberRank == 3 && friendRank == 2) {
        sendOption = 2;
      }

      ///If user rank is 2 and friend rank is 1 then send option = 2
      if (AppConstants.userMemberRank == 2 && friendRank == 1) {
        sendOption = 1;
      }
      return Positioned(
        bottom: 0,
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 10),
          width: MediaQuery.of(context).size.width,
          decoration: BoxDecoration(
              color: AppColors.appWhite,
              border: Border.all(
                color: AppColors.lightStroke,
              ),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(25),
                topRight: Radius.circular(25),
              )),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              //Send invite
              Padding(
                padding: const EdgeInsets.only(left: 10),
                child: Text(
                  "Send invite",
                  style: TextStyle(
                    fontFamily: "LatoSemibold",
                    fontSize: 15,
                    fontWeight: FontWeight.w600,
                    color: AppColors.writingColor2,
                  ),
                ),
              ),
              verticalSizedBox(20),
              //Send member invite
              sendOption == 1 || sendOption == 3
                  ? InkWell(
                      onTap: () {
                        membershipBloc.createInviteCode(
                            phoneNumber, "MEMBER", name);
                      },
                      child: Container(
                        width: double.infinity,
                        alignment: Alignment.center,
                        padding: const EdgeInsets.symmetric(vertical: 15),
                        decoration: BoxDecoration(
                            color: AppColors.textFieldFill1,
                            borderRadius:
                                BorderRadius.all(Radius.circular(10))),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            SvgPicture.asset(AppImages.starBlank),
                            horizontalSizedBox(10),
                            Text(
                              "Send member invite  [${BuyerHomeBloc.userDetailsResponse.userDetail!.memberInviteBalance!} left]",
                              style: TextStyle(
                                fontFamily: "LatoBold",
                                fontSize: 15,
                                fontWeight: FontWeight.w700,
                                color: AppColors.writingColor2,
                              ),
                            ),
                          ],
                        ),
                      ),
                    )
                  : const SizedBox(),
              verticalSizedBox(10),
              //Send seller invite  [1 left]
              sendOption == 2 || sendOption == 3
                  ? InkWell(
                      onTap: () {
                        membershipBloc.createInviteCode(
                            phoneNumber, "SELLER", name);
                      },
                      child: Container(
                        width: double.infinity,
                        alignment: Alignment.center,
                        padding: const EdgeInsets.symmetric(vertical: 15),
                        decoration: BoxDecoration(
                            color: AppColors.textFieldFill1,
                            borderRadius:
                                BorderRadius.all(Radius.circular(10))),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            SvgPicture.asset(
                              AppImages.messageIcon,
                              color: AppColors.darkGray,
                            ),
                            horizontalSizedBox(10),
                            Text(
                              "Send seller invite  [${BuyerHomeBloc.userDetailsResponse.userDetail!.sellerInviteBalance!} left]",
                              style: TextStyle(
                                fontFamily: "LatoBold",
                                fontSize: 15,
                                fontWeight: FontWeight.w700,
                                color: AppColors.writingColor2,
                              ),
                            ),
                          ],
                        ),
                      ),
                    )
                  : const SizedBox(),
              const ShareMembershipAppButton()
            ],
          ),
        ),
      );
    }

    if (sendRequest == "request") {
      /// Request option 1 -> Request member invite, 2 -> Request seller invite and 3 -> Both member and seller
      int requestOptions = 0;

      ///If user rank is 1 and friend rank is 3 then send option = 3
      if (AppConstants.userMemberRank == 1 && friendRank == 3) {
        requestOptions = 3;
      }

      ///If user rank is 2 and friend rank is 3 then send option = 2
      if (AppConstants.userMemberRank == 2 && friendRank == 3) {
        requestOptions = 2;
      }

      ///If user rank is 1 and friend rank is 2 then send option = 2
      if (AppConstants.userMemberRank == 1 && friendRank == 2) {
        requestOptions = 1;
      }

      ///If user rank is 0
      if (AppConstants.userMemberRank == 0) {
        requestOptions = 3;
      }
      //print(requestOptions);

      return Positioned(
        bottom: 0,
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 10),
          width: MediaQuery.of(context).size.width,
          decoration: BoxDecoration(
              color: AppColors.appWhite,
              border: Border.all(
                color: AppColors.lightStroke,
              ),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(25),
                topRight: Radius.circular(25),
              )),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              //Request invite
              Padding(
                padding: EdgeInsets.only(left: 10),
                child: Text(
                  "Request invite",
                  style: TextStyle(
                    fontFamily: "LatoSemibold",
                    fontSize: 15,
                    fontWeight: FontWeight.w600,
                    color: AppColors.writingColor2,
                  ),
                ),
              ),
              verticalSizedBox(20),
              //Request member invite
              requestOptions == 1 || requestOptions == 3
                  ? InkWell(
                      onTap: () {
                        // membershipBloc.createInviteCode(phoneNumber,"MEMBER");
                      },
                      child: Container(
                        width: double.infinity,
                        alignment: Alignment.center,
                        padding: const EdgeInsets.symmetric(vertical: 15),
                        decoration: BoxDecoration(
                            color: AppColors.textFieldFill1,
                            borderRadius:
                                BorderRadius.all(Radius.circular(10))),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            SvgPicture.asset(AppImages.starBlank),
                            horizontalSizedBox(10),
                            Text(
                              "Request member invite",
                              style: TextStyle(
                                fontFamily: "LatoBold",
                                fontSize: 15,
                                fontWeight: FontWeight.w700,
                                color: AppColors.writingColor2,
                              ),
                            ),
                          ],
                        ),
                      ),
                    )
                  : const SizedBox(),
              verticalSizedBox(10),
              //Send seller invite  [1 left]
              requestOptions == 2 || requestOptions == 3
                  ? Container(
                      width: double.infinity,
                      alignment: Alignment.center,
                      padding: const EdgeInsets.symmetric(vertical: 15),
                      decoration: BoxDecoration(
                          color: AppColors.textFieldFill1,
                          borderRadius: BorderRadius.all(Radius.circular(10))),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          SvgPicture.asset(
                            AppImages.messageIcon,
                            color: AppColors.darkGray,
                          ),
                          horizontalSizedBox(10),
                          Text(
                            "Request seller invite",
                            style: TextStyle(
                              fontFamily: "LatoBold",
                              fontSize: 15,
                              fontWeight: FontWeight.w700,
                              color: AppColors.writingColor2,
                            ),
                          ),
                        ],
                      ),
                    )
                  : const SizedBox()
            ],
          ),
        ),
      );
    }

    return Container();
  }

  //endregion

  //region Send member invite
  Widget sendMemberInvite(String phoneNumber, String name) {
    return InkWell(
      onTap: () {
        membershipBloc.createInviteCode(phoneNumber, "MEMBER", name);
      },
      child: Container(
        width: double.infinity,
        alignment: Alignment.center,
        padding: const EdgeInsets.symmetric(vertical: 15),
        decoration: BoxDecoration(
            color: AppColors.textFieldFill1,
            borderRadius: BorderRadius.all(Radius.circular(10))),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            SvgPicture.asset(AppImages.starBlank),
            horizontalSizedBox(10),
            Text(
              "Send member invite  [${BuyerHomeBloc.userDetailsResponse.userDetail!.memberInviteBalance!} left]",
              style: TextStyle(
                fontFamily: "LatoBold",
                fontSize: 15,
                fontWeight: FontWeight.w700,
                color: AppColors.writingColor2,
              ),
            ),
          ],
        ),
      ),
    );
  }

  //endregion

  //region Send Seller Invite
  Widget sendSellerInvite(String phoneNumber, String name) {
    return InkWell(
      onTap: () {
        membershipBloc.createInviteCode(phoneNumber, "SELLER", name);
      },
      child: Container(
        width: double.infinity,
        alignment: Alignment.center,
        padding: const EdgeInsets.symmetric(vertical: 15),
        decoration: BoxDecoration(
            color: AppColors.textFieldFill1,
            borderRadius: BorderRadius.all(Radius.circular(10))),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            SvgPicture.asset(
              AppImages.messageIcon,
              color: AppColors.darkGray,
            ),
            horizontalSizedBox(10),
            Text(
              "Send seller invite  [${BuyerHomeBloc.userDetailsResponse.userDetail!.sellerInviteBalance!} left]",
              style: TextStyle(
                fontFamily: "LatoBold",
                fontSize: 15,
                fontWeight: FontWeight.w700,
                color: AppColors.writingColor2,
              ),
            ),
          ],
        ),
      ),
    );
  }

  //endregion

  //region Filter
  Widget filter() {
    return Stack(
      alignment: Alignment.topRight,
      children: [
        InkWell(
            onTap: () {
              membershipBloc.onTapDrawer();
            },
            child: const SizedBox(
              height: double.infinity,
              width: double.infinity,
            )),
        Padding(
          padding: const EdgeInsets.only(right: 30, top: 100),
          child: Container(
              width: 150,
              decoration: BoxDecoration(
                color: AppColors.appWhite,
                border: Border.all(color: AppColors.lightGray.withOpacity(0.2)),
                boxShadow: [
                  BoxShadow(
                    offset: const Offset(1, 1),
                    blurRadius: 5,
                    color: AppColors.appBlack.withOpacity(0.2),
                  ),
                ],
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  //Seller
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Checkbox(
                          value: membershipBloc.sellerFilter,
                          activeColor: AppColors.primaryGreen,
                          onChanged: (value) {
                            membershipBloc.sellerFilter =
                                !membershipBloc.sellerFilter;
                            membershipBloc.onSelectFilter();
                          }),
                      Text(
                        "seller",
                        textAlign: TextAlign.left,
                        style: TextStyle(
                            fontSize: 14,
                            fontFamily: "LatoBold",
                            fontWeight: FontWeight.w700,
                            color: AppColors.writingColor2),
                      ),
                    ],
                  ),
                  divider(),
                  //Seller
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Checkbox(
                          value: membershipBloc.memberFilter,
                          activeColor: AppColors.primaryGreen,
                          onChanged: (value) {
                            membershipBloc.memberFilter =
                                !membershipBloc.memberFilter;
                            membershipBloc.onSelectFilter();
                          }),
                      Text(
                        "member",
                        textAlign: TextAlign.left,
                        style: TextStyle(
                            fontSize: 14,
                            fontFamily: "LatoBold",
                            fontWeight: FontWeight.w700,
                            color: AppColors.writingColor2),
                      ),
                    ],
                  ),
                  divider(),
                  //Non member
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Checkbox(
                          value: membershipBloc.nonMemberFilter,
                          activeColor: AppColors.primaryGreen,
                          onChanged: (value) {
                            membershipBloc.nonMemberFilter =
                                !membershipBloc.nonMemberFilter;
                            membershipBloc.onSelectFilter();
                          }),
                      Text(
                        "non-member",
                        textAlign: TextAlign.left,
                        style: TextStyle(
                            fontSize: 14,
                            fontFamily: "LatoBold",
                            fontWeight: FontWeight.w700,
                            color: AppColors.writingColor2),
                      ),
                    ],
                  ),
                  divider(),
                  //Contact only
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Checkbox(
                          value: membershipBloc.contactOnlyFilter,
                          activeColor: AppColors.primaryGreen,
                          onChanged: (value) {
                            membershipBloc.contactOnlyFilter =
                                !membershipBloc.contactOnlyFilter;
                            membershipBloc.onSelectFilter();
                          }),
                      Text(
                        "contact only",
                        textAlign: TextAlign.left,
                        style: TextStyle(
                            fontSize: 14,
                            fontFamily: "LatoBold",
                            fontWeight: FontWeight.w700,
                            color: AppColors.writingColor2),
                      ),
                    ],
                  ),
                  divider(),
                ],
              )),
        ),
      ],
    );
  }

  //endregion

//region Already send
  Widget alreadySend() {
    return Positioned(
      bottom: 0,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 10),
        width: MediaQuery.of(context).size.width,
        decoration: BoxDecoration(
            color: AppColors.appWhite,
            border: Border.all(
              color: AppColors.lightStroke,
            ),
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(25),
              topRight: Radius.circular(25),
            )),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            //Send invite
            Padding(
              padding: EdgeInsets.only(left: 10),
              child: Text(
                "Send invite",
                style: TextStyle(
                  fontFamily: "LatoSemibold",
                  fontSize: 15,
                  fontWeight: FontWeight.w600,
                  color: AppColors.writingColor2,
                ),
              ),
            ),
            verticalSizedBox(20),
            InkWell(
              onTap: () {
                // membershipBloc.createInviteCode(phoneNumber,"MEMBER",name);
              },
              child: Container(
                width: double.infinity,
                alignment: Alignment.center,
                padding: const EdgeInsets.symmetric(vertical: 15),
                decoration: BoxDecoration(
                    color: AppColors.textFieldFill1,
                    borderRadius: BorderRadius.all(Radius.circular(10))),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    SvgPicture.asset(AppImages.starBlank),
                    horizontalSizedBox(10),
                    Text(
                      "Alredy shared",
                      style: TextStyle(
                        fontFamily: "LatoBold",
                        fontSize: 15,
                        fontWeight: FontWeight.w700,
                        color: AppColors.writingColor2,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
//endregion
}
