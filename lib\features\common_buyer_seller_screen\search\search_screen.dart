import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/common_buyer_seller_screen/search/search_bloc.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_search_field/app_search_field.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

class SearchScreen extends StatefulWidget {
  final List<String> dataList;
  final String searchTitle;
  final bool isAddFeatureEnable;
  final bool? isSearchVisible;
  final bool? shortData;

  const SearchScreen(
      {Key? key,
      required this.dataList,
      required this.searchTitle,
      required this.isAddFeatureEnable,
      this.isSearchVisible = true,
      this.shortData = true})
      : super(key: key);

  @override
  State<SearchScreen> createState() => _SearchScreenState();
}

class _SearchScreenState extends State<SearchScreen> {
  //region Bloc
  late SearchBloc searchBloc;

  //endregion
  //region Init
  @override
  void initState() {
    searchBloc = SearchBloc(context, widget.dataList, widget.shortData);
    searchBloc.init();
    super.initState();
  }

  //endregion
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.appWhite,
      appBar: appBar(),
      body: GestureDetector(
          onTap: () {
            CommonMethods.closeKeyboard(context);
          },
          child: SafeArea(child: body())),
    );
  }

  //region Appbar
  AppBar appBar() {
    return AppCommonWidgets.mainAppBar(
      context: context,
      isCustomTitle: false,
      title: widget.searchTitle,
      isDefaultMenuVisible: true,
      isMembershipVisible: false,
      isCartVisible: false,
    );
  }

  //endregion

  //region body
  Widget body() {
    return StreamBuilder<bool>(
        stream: searchBloc.refreshCtrl.stream,
        builder: (context, snapshot) {
          return Column(
            children: [
              Visibility(
                visible: widget.isSearchVisible!,
                child: Container(
                  margin:
                      const EdgeInsets.only(left: 10, right: 10, bottom: 20),
                  child: AppSearchField(
                    textEditingController: searchBloc.searchTextCtrl,
                    isAutoFocus: false,
                    hintText: 'Search or add',
                    onChangeText: (value) {
                      searchBloc.onSearch();
                    },
                    onTapSuffix: () {
                      searchBloc.onSearch();
                    },
                  ),
                ),
              ),
              //Add
              Visibility(
                  visible: searchBloc.searchTextCtrl.text.trim().isNotEmpty &&
                      widget.isSearchVisible! &&
                      widget.isAddFeatureEnable,
                  child: Container(
                    margin: const EdgeInsets.symmetric(horizontal: 10),
                    padding: const EdgeInsets.all(10),
                    child: Row(
                      mainAxisSize: MainAxisSize.max,
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                            child: Text(
                          searchBloc.searchTextCtrl.text,
                          style: AppTextStyle.contentText0(
                              textColor: AppColors.appBlack),
                        )),
                        InkWell(
                            onTap: () {
                              searchBloc.onTapAdd(
                                  value: searchBloc.searchTextCtrl.text);
                            },
                            child: Container(
                                padding:
                                    const EdgeInsets.symmetric(horizontal: 20),
                                child: Text(
                                  AppStrings.add,
                                  style: AppTextStyle.contentHeading0(
                                      textColor: AppColors.brandBlack),
                                )))
                      ],
                    ),
                  )),
              Expanded(child: dataList())
            ],
          );
        });
  }

//endregion

//region List
  Widget dataList() {
    return Scrollbar(
      interactive: true,
      radius: const Radius.circular(50),
      thickness: 5,
      trackVisibility: true,
      child: ListView.builder(
          shrinkWrap: true,
          itemCount: searchBloc.searchedCategoryList.length,
          itemBuilder: (context, index) {
            return CupertinoButton(
              onPressed: () {
                searchBloc.onTapCategory(
                    value: searchBloc.searchedCategoryList[index]);
              },
              padding: EdgeInsets.zero,
              child: Container(
                margin: const EdgeInsets.symmetric(horizontal: 10),
                width: double.infinity,
                decoration: BoxDecoration(
                    border: Border(
                  bottom: BorderSide(
                    color: AppColors
                        .textFieldFill1, // Replace with your desired color
                  ),
                )),
                padding: const EdgeInsets.all(10),
                child: Text(
                  searchBloc.searchedCategoryList[index],
                  style:
                      AppTextStyle.contentText0(textColor: AppColors.appBlack),
                ),
              ),
            );
          }),
    );
  }
//endregion
}
