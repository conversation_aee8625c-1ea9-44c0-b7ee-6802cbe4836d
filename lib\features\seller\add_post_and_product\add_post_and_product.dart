import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/data_model/store_dashboard_data_model/store_dashboard_data_model.dart';
import 'package:swadesic/features/post/add_post/add_post_screen.dart';
import 'package:swadesic/features/seller/add_product/add_product_screen.dart';
import 'package:swadesic/features/seller/seller_store/seller_trust_center/seller_trust_center_screen.dart';
import 'package:swadesic/features/story/add_story/add_story_screen.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';

class AddPostAndProduct extends StatefulWidget {
  const AddPostAndProduct({super.key});

  @override
  State<AddPostAndProduct> createState() => _AddPostAndProductState();
}

class _AddPostAndProductState extends State<AddPostAndProduct> {
  @override
  Widget build(BuildContext context) {
    return body();
  }

  //region Body
  Widget body(){
    return Container(
        margin: const EdgeInsets.symmetric(vertical: 30,horizontal: 10),
        child: Column(
          children: [
            InkWell(
                onTap: (){
                  Navigator.pop(context);
                  var screen =  const AddPostScreen();
                  var route = MaterialPageRoute(builder: (context) => screen);
                  Navigator.push(AppConstants.userStoreCommonBottomNavigationContext, route);

                },
                child: card(icon: AppImages.addAPost,subTitle: AppStrings.addAPost, color: Color.fromARGB(255, 41, 41, 41))),
            Divider(color: AppColors.disableBlack,),
            InkWell(
                onTap: (){
                  Navigator.pop(context);
                  var screen =   AddProductScreen(storeId: AppConstants.appData.storeId!, storeReference: AppConstants.appData.storeReference!);
                  var route = MaterialPageRoute(builder: (context) => screen);
                  Navigator.push(AppConstants.currentSelectedTabContext, route);

                },
                child: card(icon: AppImages.tabProductActive, subTitle:AppStrings.addAProduct )),
            // const Divider(color: AppColors.disableBlack,),
            // InkWell(
            //     onTap: (){
            //       Navigator.pop(context);
            //       var screen = const AddStoryScreen();
            //       var route = MaterialPageRoute(builder: (context) => screen);
            //       Navigator.push(AppConstants.userStoreCommonBottomNavigationContext, route);
            //
            //     },
            //     child: card(icon: AppImages.tabProductActive, subTitle:AppStrings.addAStory )),
          ],
        )
    );
  }
//endregion



//region Card
  Widget card({ required String subTitle, required String icon, Color? color}){
    color??= AppColors.appBlack;
    return Container(
      padding: const EdgeInsets.all(10),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SvgPicture.asset(icon,height: 25,width: 25,color: color),
          const SizedBox(width: 15,),
          Expanded(
            child: Text(subTitle,
              maxLines: 1,
              style: AppTextStyle.settingHeading1(textColor: AppColors.writingBlack0),),
          )

        ],
      ),
    );
  }
//endregion


}
