import 'package:flutter/material.dart';
import 'package:swadesic/features/seller/add_image/add_image_bloc.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_widgets.dart';

class AddImageScreen extends StatefulWidget {
  final bool? selectSingleImage;
  final String? customTitle;
  const AddImageScreen({Key? key, this.selectSingleImage, this.customTitle})
      : super(key: key);

  @override
  _BrandNameScreenState createState() => _BrandNameScreenState();
}

class _BrandNameScreenState extends State<AddImageScreen> {
  //region Bloc
  late AddImageBloc addImageBloc;

  //endregion

  //region Init
  @override
  void initState() {
    addImageBloc = AddImageBloc(context);
    addImageBloc.init();
    super.initState();
  }

  //endregion
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.appWhite,
      appBar: appBar(),
      body: body(),
    );
  }

  //region Appbar
  AppBar appBar() {
    return AppCommonWidgets.mainAppBar(
      context: context,
      isCustomTitle: false,
      title: widget.customTitle ?? AppStrings.productImage,
      isDefaultMenuVisible: false,
      isMembershipVisible: false,
      isCartVisible: false,
      isTextButtonVisible: false,
    );
  }

  //endregion

  //region AppBar
  // AppBar appBar(){
  //   return AppBar(
  //
  //     backgroundColor: AppColors.white,
  //     leading: CupertinoButton(
  //         onPressed: (){
  //           Navigator.pop(context);
  //         },
  //
  //         padding: EdgeInsets.zero,
  //         child: SvgPicture.asset(AppImages.backButton,color: AppColors.appBlack,fit: BoxFit.fill)),
  //     elevation: 0,
  //     centerTitle: false,
  //     titleSpacing: 0,
  //
  //     title: Text(
  //       AppStrings.productImage,
  //       style: TextStyle(
  //         fontSize: 19,
  //         fontWeight: FontWeight.w700,
  //         color: AppColors.appBlack,
  //       ),
  //     ),
  //     automaticallyImplyLeading: false,
  //
  //     actions: [
  //
  //       CupertinoButton(
  //           onPressed: (){},
  //           child: SvgPicture.asset(AppImages.drawerIcon,color: AppColors.appBlack,height: 24,)),
  //
  //
  //     ],
  //
  //     //endregion
  //   );
  // }

  //endregion

  //region body
  Widget body() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          takePhoto(),
          verticalSizedBox(40),
          uploadImageFromPhone(),
          verticalSizedBox(40),

          //addFromPrevious(),
        ],
      ),
    );
  }
//endregion

  //region Take A Photo
  Widget takePhoto() {
    return button(
        onTap: () {
          widget.selectSingleImage != null
              ? addImageBloc.openCameraSelectSingleImage()
              : addImageBloc.openCamera();
        },
        buttonName: AppStrings.takePhoto);
  }
  //endregion

  //region Upload Image From Phone
  Widget uploadImageFromPhone() {
    return button(
        onTap: () {
          widget.selectSingleImage != null
              ? addImageBloc.openGallerySingleImage()
              : addImageBloc.openGallery();
        },
        buttonName: AppStrings.uploadImageFromPhone);
  }
  //endregion

//region Button
  Widget button({required String buttonName, required dynamic onTap}) {
    return InkWell(
      onTap: () {
        onTap();
      },
      child: Container(
        alignment: Alignment.center,
        width: double.infinity,
        padding: const EdgeInsets.all(15),
        decoration: BoxDecoration(
            color: AppColors.brandBlack,
            borderRadius: BorderRadius.all(Radius.circular(100))),
        child: Text(buttonName,
            style: AppTextStyle.access0(textColor: AppColors.appWhite)),
      ),
    );
  }
//endregion
}
