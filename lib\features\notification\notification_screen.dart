import 'dart:ui';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/buyers/buyer_home/buyer_home_bloc.dart';
import 'package:swadesic/features/data_model/notification_data_mdel/all_store_notification.dart';
import 'package:swadesic/features/data_model/notification_data_mdel/user_or_store_notification.dart';
import 'package:swadesic/features/notification/all_store_notification/all_store_notification.dart';
import 'package:swadesic/features/notification/notification_bloc.dart';
import 'package:swadesic/features/notification/notification_common_widget/notification_common_widget.dart';
import 'package:swadesic/features/notification/user_or_store_notification/user_or_store_notification.dart';
import 'package:swadesic/model/notification_response/notification_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:swadesic/util/auto_hide_navigation_service.dart';
import 'package:visibility_detector/visibility_detector.dart';

// region Notification
class NotificationScreen extends StatefulWidget {
  final int? storeId;

  const NotificationScreen({Key? key, this.storeId}) : super(key: key);

  @override
  _NotificationScreenState createState() => _NotificationScreenState();
}
// endregion

class _NotificationScreenState extends State<NotificationScreen>
    with
        SingleTickerProviderStateMixin,
        AutoHideNavigationMixin<NotificationScreen> {
  // region Bloc
  late NotificationBloc notificationBloc;
  late TabController notificationTabCtrl =
      TabController(length: 2, vsync: this, initialIndex: 0);
  // endregion

  // region Init
  @override
  void initState() {
    notificationBloc =
        NotificationBloc(context, widget.storeId ?? 0, notificationTabCtrl);
    notificationBloc.init();

    // Enable auto-hide navigation for notification screen
    enableAutoHideNavigation();

    super.initState();
  }

  // endregion

  //region Dispose
  @override
  void dispose() {
    // Disable auto-hide navigation
    disableAutoHideNavigation();

    notificationBloc.dispose();
    super.dispose();
  }

  //endregion

  // region build
  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 2,
      child: Scaffold(
        backgroundColor: AppColors.appWhite,
        appBar: appBar(),
        resizeToAvoidBottomInset: true,
        body: SafeArea(
          child: SizedBox(
            height: MediaQuery.of(context).size.height,
            child: body(),
          ),
        ),
      ),
    );
  }

  // endregion

  //region AppBar
  AppBar appBar() {
    return AppCommonWidgets.mainAppBar(
      onTapLeading: () {
        //Move back to one single tab in buyer
        AppConstants.appData.isUserView!
            ? AppConstants.userPersistentTabController
                .jumpToTab(AppConstants.userPersistentTabController.index - 2)
            :
            //Move back to one single tab in seller
            AppConstants.storePersistentTabController
                .jumpToTab(AppConstants.storePersistentTabController.index - 2);
      },
      context: context,
      isLeadingVisible: false,
      isCustomTitle: false,
      title: AppStrings.notifications,
      isMembershipVisible: false,
      isCustomMenuVisible: true,
      isDefaultMenuVisible: false,
      customMenuButton: myMenuButton(),
      isCartVisible: false,
    );
  }

  //endregion

  //region Menu button
  Widget myMenuButton() {
    return PopupMenuButton(
      padding: EdgeInsets.zero,
      icon: SvgPicture.asset(AppImages.drawerIcon, color: AppColors.appBlack),
      itemBuilder: (context) {
        return [
          ///Report
          PopupMenuItem<int>(
            height: 0,
            value: 0,
            onTap: () async {
              // notificationBloc.openReportDialog();
              CommonMethods.reportAndSuggestion(context: context);
            },
            padding: EdgeInsets.zero,
            child: SizedBox(
              width: 150,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                      padding: const EdgeInsets.all(10),
                      child:
                          AppCommonWidgets.menuText(text: AppStrings.report)),
                  divider()
                ],
              ),
            ),
          ),

          ///Mark all as read
          PopupMenuItem<int>(
            height: 0,
            value: 0,
            onTap: () {
              notificationBloc.onTapMarkAllAsRead();
              //notificationBloc.markAllRead();
              //userProfileBloc.onTapShareUserProfile(userName: UserProfileBloc.getUserDetailsResponse.userDetail!.userName!);
            },
            padding: EdgeInsets.zero,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Container(
                    padding: const EdgeInsets.all(10),
                    child: AppCommonWidgets.menuText(
                        text: AppStrings.markAllAsRead)

                    // child: appText(AppStrings.shareProfile,
                    //     fontFamily: AppConstants.rRegular,
                    //     fontSize: 14,
                    //     fontWeight: FontWeight.w700
                    // )

                    ),
                // divider()
              ],
            ),
          ),
        ];
      },
    );
  }

  //endregion

  //region Body
  Widget body() {
    return Column(
      children: [
        ///Tab bar
        StreamBuilder<bool>(
            stream: notificationBloc.tabRefreshCtrl.stream,
            builder: (context, snapshot) {
              return SizedBox(
                height: 40, // Reduced height from default
                child: TabBar(
                  controller: notificationBloc.notificationTabCtrl,
                  indicator: UnderlineTabIndicator(
                    borderSide: BorderSide(
                      color: AppColors.appBlack,
                      width: 2.0,
                    ),
                  ),
                  labelPadding: EdgeInsets.zero,
                  onTap: (index) {
                    notificationBloc.tabRefreshCtrl.sink.add(true);
                  },
                  padding: EdgeInsets.zero,
                  // isScrollable: true,
                  tabs: AppConstants.appData.isUserView!
                      ? buyerTabBar() // Buyer tab
                      : storeTabBar(), // Store view
                ),
              );
            }),
        divider(),

        ///Tab view
        Expanded(
          child: TabBarView(
              controller: notificationBloc.notificationTabCtrl,
              children: AppConstants.appData.isUserView!
                  ?
                  //Buyer tav view
                  buyerTabView()
                  :
                  //Store tab view
                  storeTabView()),
        ),
      ],
    );
  }

  //endregion

//region Buyer tav bar
  List<Widget> buyerTabBar() {
    //User or store notification
    UserOrStoreNotificationDataModel userOrStoreNotificationDataModel =
        Provider.of<UserOrStoreNotificationDataModel>(context);
    //All store notification
    AllStoreNotificationDataModel allStoreNotificationDataModel =
        Provider.of<AllStoreNotificationDataModel>(context);

    return [
      //Personal
      SizedBox(
        height: kToolbarHeight,
        child: Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(
              AppStrings.personal,
              style: AppTextStyle.settingHeading1(
                  textColor: notificationBloc.notificationTabCtrl.index == 0
                      ? AppColors.appBlack
                      : AppColors.writingBlack1),
            ),
            Visibility(
              visible: userOrStoreNotificationDataModel
                      .userOrStoreNotificationResponse!.notSeenCount !=
                  0,
              child: Container(
                margin: const EdgeInsets.only(left: 10),
                padding: const EdgeInsets.all(5),
                decoration:
                    BoxDecoration(shape: BoxShape.circle, color: AppColors.red),
                child: Text(
                    userOrStoreNotificationDataModel
                        .userOrStoreNotificationResponse!.notSeenCount
                        .toString(),
                    style: TextStyle(
                        color: AppColors.appWhite,
                        fontSize: 10,
                        fontWeight: FontWeight.w700)),
              ),
            ),
          ],
        ),
      ),
      //Business
      SizedBox(
        height: kToolbarHeight,
        child: Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(
              AppStrings.business,
              style: AppTextStyle.settingHeading1(
                  textColor: notificationBloc.notificationTabCtrl.index == 1
                      ? AppColors.appBlack
                      : AppColors.writingBlack1),
            ),
            Visibility(
              visible: allStoreNotificationDataModel
                      .allStoreNotificationResponse!.notSeenCount !=
                  0,
              child: Container(
                margin: const EdgeInsets.only(left: 10),
                padding: const EdgeInsets.all(5),
                decoration:
                    BoxDecoration(shape: BoxShape.circle, color: AppColors.red),
                child: Text(
                    allStoreNotificationDataModel
                        .allStoreNotificationResponse!.notSeenCount
                        .toString(),
                    style: TextStyle(
                        color: AppColors.appWhite,
                        fontSize: 10,
                        fontWeight: FontWeight.w700)),
              ),
            ),
          ],
        ),
      ),
    ];
  }

//endregion

//region Store tav bar
  List<Widget> storeTabBar() {
    //User or store notification
    UserOrStoreNotificationDataModel userOrStoreNotificationDataModel =
        Provider.of<UserOrStoreNotificationDataModel>(context);
    //All store notification
    AllStoreNotificationDataModel allStoreNotificationDataModel =
        Provider.of<AllStoreNotificationDataModel>(context);

    return [
      //Business
      SizedBox(
        height: kToolbarHeight,
        child: Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(
              AppStrings.business,
              style: AppTextStyle.settingHeading1(
                  textColor: notificationBloc.notificationTabCtrl.index == 0
                      ? AppColors.appBlack
                      : AppColors.writingBlack1),
            ),
            Visibility(
              visible: allStoreNotificationDataModel
                      .allStoreNotificationResponse!.notSeenCount !=
                  0,
              child: Container(
                margin: const EdgeInsets.only(left: 10),
                padding: const EdgeInsets.all(5),
                decoration:
                    BoxDecoration(shape: BoxShape.circle, color: AppColors.red),
                child: Text(
                    allStoreNotificationDataModel
                        .allStoreNotificationResponse!.notSeenCount
                        .toString(),
                    style: TextStyle(
                        color: AppColors.appWhite,
                        fontSize: 10,
                        fontWeight: FontWeight.w700)),
              ),
            ),
          ],
        ),
      ),
      //Personal
      SizedBox(
        height: kToolbarHeight,
        child: Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(
              AppStrings.personal,
              style: AppTextStyle.settingHeading1(
                  textColor: notificationBloc.notificationTabCtrl.index == 1
                      ? AppColors.appBlack
                      : AppColors.writingBlack1),
            ),
            Visibility(
              visible: userOrStoreNotificationDataModel
                      .userOrStoreNotificationResponse!.notSeenCount !=
                  0,
              child: Container(
                margin: const EdgeInsets.only(left: 10),
                padding: const EdgeInsets.all(5),
                decoration:
                    BoxDecoration(shape: BoxShape.circle, color: AppColors.red),
                child: Text(
                    userOrStoreNotificationDataModel
                        .userOrStoreNotificationResponse!.notSeenCount
                        .toString(),
                    style: TextStyle(
                        color: AppColors.appWhite,
                        fontSize: 10,
                        fontWeight: FontWeight.w700)),
              ),
            ),
          ],
        ),
      ),
    ];
  }

//endregion

//region Buyer tab view
  List<Widget> buyerTabView() {
    return [
      UserOrStoreNotification(
        unSeenCount: (data) {
          //print("Total unseen in user/store ${data}");
        },
      ),
      AllStoreNotification(
        unSeenCount: (data) {
          //print("Total unseen in all store ${data}");
        },
      ),
    ];
  }

//endregion

// region Buyer tab view
  List<Widget> storeTabView() {
    return [
      AllStoreNotification(
        unSeenCount: (data) {
          //print("Total unseen in all store ${data}");
        },
      ),
      UserOrStoreNotification(
        unSeenCount: (data) {
          //print("Total unseen in user/store ${data}");
        },
      ),
    ];
  }
//endregion
}
