import 'dart:io';
import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pretty_qr_code/pretty_qr_code.dart';
import 'package:swadesic/features/external_reviews/external_review_link_created_bloc.dart';
import 'package:swadesic/features/share_with_image/share_with_image_screen.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_enums.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/features/widgets/custom_image_container/custom_image_container.dart';

class ExternalReviewLinkCreatedScreen extends StatefulWidget {
  final String token;
  final String expiresAt;
  final String userIdentifier;
  final String productReference;
  final String productName;
  final String? productImage;
  final String storeHandle;

  const ExternalReviewLinkCreatedScreen({
    Key? key,
    required this.token,
    required this.expiresAt,
    required this.userIdentifier,
    required this.productReference,
    required this.productName,
    this.productImage,
    required this.storeHandle,
  }) : super(key: key);

  @override
  ExternalReviewLinkCreatedScreenState createState() =>
      ExternalReviewLinkCreatedScreenState();
}

class ExternalReviewLinkCreatedScreenState
    extends State<ExternalReviewLinkCreatedScreen> {
  late ExternalReviewLinkCreatedBloc bloc;
  final GlobalKey _qrKey = GlobalKey();

  @override
  void initState() {
    super.initState();
    bloc = ExternalReviewLinkCreatedBloc(
      context,
      widget.token,
      widget.expiresAt,
      widget.userIdentifier,
      widget.productReference,
      widget.storeHandle,
    );
  }

  @override
  void dispose() {
    // Schedule the flag reset for the next frame to avoid accessing deactivated widgets
    WidgetsBinding.instance.addPostFrameCallback((_) {
      AppConstants.isSignInScreenOpenedForStatisUser = false;
    });
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // Blurred background with tap to close
        Positioned.fill(
          child: GestureDetector(
            onTap: () {
              Navigator.of(context).pop();
            },
            child: BackdropFilter(
              filter: ui.ImageFilter.blur(sigmaX: 8, sigmaY: 8),
              child: Container(
                color: AppColors.appBlack.withOpacity(0.3),
              ),
            ),
          ),
        ),
        // Dialog content
        Center(
          child: GestureDetector(
            onTap: () {
              // Prevent tap from propagating to the background
            },
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 20),
              child: _buildDialogContent(),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDialogContent() {
    return RepaintBoundary(
      key: _qrKey,
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: AppColors.appWhite,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            // Bottom shadow
            BoxShadow(
              color: AppColors.appBlack.withOpacity(1),
              blurRadius: 20,
              offset: const Offset(0, 10),
            ),
            // Top shadow
            BoxShadow(
              color: AppColors.appBlack.withOpacity(0.1),
              blurRadius: 15,
              offset: const Offset(0, -5),
            ),
            // Left shadow
            BoxShadow(
              color: AppColors.appBlack.withOpacity(0.1),
              blurRadius: 15,
              offset: const Offset(-5, 0),
            ),
            // Right shadow
            BoxShadow(
              color: AppColors.appBlack.withOpacity(0.1),
              blurRadius: 15,
              offset: const Offset(5, 0),
            ),
          ],
        ),
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Combined QR Code and Link Section
              _buildCombinedSection(),
              const SizedBox(height: 24),
              _buildShareLinkButton(),
              const SizedBox(height: 16),
              Text(
                bloc.getFormattedExpiryDate(),
                style: AppTextStyle.subTitle(textColor: AppColors.appBlack),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCombinedSection() {
    return Column(
      children: [
        // Title
        Text(
          'External Review Link Created',
          style: AppTextStyle.heading1Bold(
            textColor: AppColors.appBlack,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 20),

        // QR Code
        SizedBox(
          width: 200,
          height: 200,
          child: Stack(
            alignment: Alignment.center,
            children: [
              // QR Code with embedded square
              PrettyQrView.data(
                data: bloc.reviewLink,
                errorCorrectLevel: QrErrorCorrectLevel.H,
                decoration: PrettyQrDecoration(
                  shape: PrettyQrSmoothSymbol(
                    color: AppColors.appBlack,
                    roundFactor: 1,
                  ),
                  // Create a white square in the middle of the QR code
                  image: PrettyQrDecorationImage(
                    image: AssetImage('assets/common_images/white_square.png'),
                    position: PrettyQrDecorationImagePosition.embedded,
                    scale: 0.3,
                  ),
                ),
              ),
              // Product image or app icon in the center
              SizedBox(
                width: 50,
                height: 50,
                child: widget.productImage != null
                    ? CustomImageContainer(
                        width: 40,
                        height: 40,
                        imageUrl: widget.productImage!,
                        imageType: CustomImageContainerType.product,
                        showShadow: false,
                      )
                    : Image.asset(
                        'assets/common_images/app_icon_png.png',
                        width: 40,
                        height: 40,
                        fit: BoxFit.cover,
                      ),
              ),
            ],
          ),
        ),

        // const SizedBox(height: 16),

        // // QR Code description
        // Text(
        //   'Scan to leave a review for ${widget.productName}',
        //   textAlign: TextAlign.center,
        //   style: AppTextStyle.smallText(
        //     textColor: AppColors.writingBlack1,
        //   ),
        // ),

        const SizedBox(height: 20),

        // Link section
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: AppColors.lightGray.withOpacity(0.3),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            children: [
              Expanded(
                child: Text(
                  bloc.reviewLink,
                  style: AppTextStyle.smallText(
                    textColor: AppColors.brandBlack,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              const SizedBox(width: 12),
              GestureDetector(
                onTap: bloc.onTapCopyLink,
                child: Container(
                  padding: const EdgeInsets.all(8),
                  child: Icon(
                    Icons.copy,
                    color: AppColors.appBlack,
                    size: 16,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildShareLinkButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: () async {
          // Generate QR code image
          File? qrCodeImage = await _generateQRCodeImage();

          if (!mounted) return;

          if (qrCodeImage == null) {
            CommonMethods.toastMessage(
                "Failed to generate QR code image", context);
            return;
          }

          // Close current dialog
          Navigator.of(context).pop();

          // Open ShareWithImageScreen with QR code image
          CommonMethods.accessBottomSheet(
            screen: ShareWithImageScreen(
              url: bloc.reviewLink,
              imageType: CustomImageContainerType.product,
              entityType: EntityType.UNREGISTERED,
              message: 'Please review this product: ${widget.productName}  ',
              productName: widget.productName,
              objectReference: widget.productReference,
              attachmentImagePath: qrCodeImage.path,
            ),
            context: context,
          );
        },
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.brandBlack,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        child: Text(
          'Share the link',
          style: AppTextStyle.heading1Medium(
            textColor: AppColors.appWhite,
          ),
        ),
      ),
    );
  }

  // Generate QR code image using RepaintBoundary to capture actual QR code
  Future<File?> _generateQRCodeImage() async {
    try {
      // Create the same QR code widget as displayed in the dialog
      final qrWidget = Container(
        width: 320,
        height: 320,
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(24),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.15),
              spreadRadius: 2,
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Stack(
          alignment: Alignment.center,
          children: [
            // QR Code with embedded white square
            PrettyQrView.data(
              data: bloc.reviewLink,
              errorCorrectLevel: QrErrorCorrectLevel.H,
              decoration: PrettyQrDecoration(
                shape: PrettyQrSmoothSymbol(
                  color: AppColors.appBlack,
                  roundFactor: 1,
                ),
                // Create a white square in the middle of the QR code
                image: PrettyQrDecorationImage(
                  image: AssetImage('assets/common_images/white_square.png'),
                  position: PrettyQrDecorationImagePosition.embedded,
                  scale: 0.3,
                ),
              ),
            ),
            // Product image or app icon in the center
            SizedBox(
              width: 70,
              height: 70,
              child: widget.productImage != null
                  ? CustomImageContainer(
                      width: 60,
                      height: 60,
                      imageUrl: widget.productImage!,
                      imageType: CustomImageContainerType.product,
                      showShadow: false,
                    )
                  : Image.asset(
                      'assets/common_images/app_icon_png.png',
                      width: 60,
                      height: 60,
                      fit: BoxFit.cover,
                    ),
            ),
          ],
        ),
      );

      // Use RepaintBoundary to capture the QR code
      final GlobalKey repaintKey = GlobalKey();

      // Create a temporary widget tree to render the QR code
      final qrRepaintWidget = RepaintBoundary(
        key: repaintKey,
        child: qrWidget,
      );

      // Create a temporary overlay to render the widget off-screen
      final overlay = Overlay.of(context);
      late OverlayEntry overlayEntry;

      overlayEntry = OverlayEntry(
        builder: (context) => Positioned(
          left: -1000, // Position off-screen
          top: -1000,
          child: Material(
            color: Colors.transparent,
            child: qrRepaintWidget,
          ),
        ),
      );

      overlay.insert(overlayEntry);

      // Wait for the widget to be rendered and painted
      await Future.delayed(const Duration(milliseconds: 500));

      // Capture the rendered widget
      RenderRepaintBoundary? boundary;
      try {
        // Get the render object directly from the global key
        final renderObject = repaintKey.currentContext?.findRenderObject();
        if (renderObject is RenderRepaintBoundary) {
          boundary = renderObject;
        }
      } catch (e) {
        debugPrint('Error finding render boundary: $e');
      }

      // Remove the overlay
      overlayEntry.remove();

      if (boundary == null) {
        throw Exception('Failed to find render boundary for QR code');
      }

      final ui.Image image = await boundary.toImage(pixelRatio: 2.0);

      // Convert to bytes
      final ByteData? byteData =
          await image.toByteData(format: ui.ImageByteFormat.png);
      if (byteData == null) {
        throw Exception('Failed to convert QR code to bytes');
      }

      final Uint8List pngBytes = byteData.buffer.asUint8List();

      // Get application documents directory
      final directory = await getApplicationDocumentsDirectory();
      final path =
          '${directory.path}/external_review_qr_code_${DateTime.now().millisecondsSinceEpoch}.png';
      final file = File(path);

      // Save the image file
      await file.writeAsBytes(pngBytes);

      return file;
    } catch (e) {
      debugPrint('Error generating QR code image: $e');
      return null;
    }
  }
}
