import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';

class CommunityScreen extends StatefulWidget {
  const CommunityScreen({super.key});

  @override
  State<CommunityScreen> createState() => _CommunityScreenState();
}

class _CommunityScreenState extends State<CommunityScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: body(),
    );
  }

  //region Body
  Widget body() {
    return Container(
        margin: const EdgeInsets.symmetric(horizontal: 30),
        alignment: Alignment.center,
        child: comingSoon());
  }
//endregion

//region Coming soon
  Widget comingSoon() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        SvgPicture.asset(
          AppImages.communityIcon,
          color: AppColors.appBlack,
        ),
        const SizedBox(
          height: 77,
        ),
        Text(
          AppStrings.comingSoon,
          style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
        ),
        const SizedBox(height: 10),
        Text(
          AppStrings.onePLaceToFind,
          textAlign: TextAlign.center,
          style: AppTextStyle.contentText0(textColor: AppColors.writingBlack1),
        )
      ],
    );
  }
//endregion
}
