import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/seller/membership/membership_bloc.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/common_widgets.dart';

import '../../../../model/membership_responses/sent_request_response.dart';

class SentInvites extends StatefulWidget {
  final MembershipBloc membershipBloc;
  const SentInvites({Key? key, required this.membershipBloc}) : super(key: key);

  @override
  State<SentInvites> createState() => _SentInvitesState();
}

class _SentInvitesState extends State<SentInvites> {
  @override
  Widget build(BuildContext context) {
    return sent();
  }

  //region Sent
  //region Sent
  Widget sent(){
    return RefreshIndicator(
      onRefresh: ()async{
        await widget.membershipBloc.getSentRequestApi();
      },
      child: StreamBuilder<MembershipState>(
          stream: widget.membershipBloc.sentCtrl.stream,
          initialData:MembershipState.Loading ,
          builder: (context, snapshot) {
            if(snapshot.data == MembershipState.Success){
              return Padding(
                padding: const EdgeInsets.symmetric(horizontal: 10),
                child: Column(
                  children: [
                    searchFilter(),
                    verticalSizedBox(10),
                    Expanded(child: ListView(
                      shrinkWrap: true,
                      children: [
                        invitedAsMember(),
                        verticalSizedBox(30),
                        invitedAsSeller(),
                      ],
                    )),
                  ],
                ),
              );
            }
            return Container();
          }
      ),
    );
  }

  //endregion


  //region Invited As member
  Widget invitedAsMember(){
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        widget.membershipBloc.sentMemberInvites.isEmpty?const SizedBox()
            :Padding(
          padding: EdgeInsets.symmetric(horizontal: 10),
          child: Text("Invited as member",
            style:  TextStyle(
              fontFamily: "LatoSemibold",
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color:AppColors.writingColor2,
            ),
          ),
        ),
        ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            padding: const EdgeInsets.symmetric(horizontal: 10),
            itemCount:widget.membershipBloc.sentMemberInvites.length,
            itemBuilder:(buildContext,index){
              return Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  verticalSizedBox(15),
                 widget.membershipBloc.sentMemberInvites[index].name==null?const SizedBox():
                 Text(widget.membershipBloc.sentMemberInvites[index].name!,
                    style:  TextStyle(
                      fontFamily: "RobotoRegular",
                      fontSize: 16,
                      fontWeight: FontWeight.w400,
                      color:AppColors.appBlack,
                    ),
                  ),
                  verticalSizedBox(5),
                  //Number and Status
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      //Number
                      Text(widget.membershipBloc.sentRequestResponse.memberSellerInvite!.member![index].phoneNumber!,
                        style:  TextStyle(
                          fontFamily: "LatoRegular",
                          fontSize: 12,
                          fontWeight: FontWeight.w400,
                          color:AppColors.appBlack,
                        ),
                      ),
                      horizontalSizedBox(10),

                      //Member
                      Visibility(
                        visible:widget.membershipBloc.sentMemberInvites[index].rank == 2 && widget.membershipBloc.sentMemberInvites[index].isOnboarded!,
                        child: Container(
                          alignment: Alignment.center,
                          padding: const EdgeInsets.symmetric(horizontal: 10,vertical: 3),
                          decoration: BoxDecoration(
                              color: AppColors.inActiveGreen,
                              borderRadius: BorderRadius.all(Radius.circular(20))
                          ),
                          child:  Text("member",
                            style:  TextStyle(
                              fontFamily: "LatoRegular",
                              fontSize: 12,
                              fontWeight: FontWeight.w400,
                              color:AppColors.appBlack,
                            ),
                          ),
                        ),
                      ),
                      //Seller
                      Visibility(
                        visible:widget.membershipBloc.sentMemberInvites[index].rank == 3 && widget.membershipBloc.sentMemberInvites[index].isOnboarded!,
                        child: Container(
                          alignment: Alignment.center,
                          padding: const EdgeInsets.symmetric(horizontal: 10,vertical: 3),
                          decoration: BoxDecoration(
                              color: AppColors.purple,
                              borderRadius: BorderRadius.all(Radius.circular(20))
                          ),
                          child:  Text("seller",
                            style:  TextStyle(
                              fontFamily: "LatoRegular",
                              fontSize: 12,
                              fontWeight: FontWeight.w400,
                              color:AppColors.appBlack,
                            ),
                          ),
                        ),
                      ),

                      Expanded(child: horizontalSizedBox(10)),
                      removeJoinedAndAlreadyJoined(widget.membershipBloc.sentMemberInvites[index])
                    ],
                  ),

                ],
              );
            }),
      ],
    );
  }
  //endregion

  //region Invited As Seller
  Widget invitedAsSeller(){
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
       widget.membershipBloc.sentSellerInvites.isEmpty?const SizedBox(): Padding(
          padding: EdgeInsets.symmetric(horizontal: 10),
          child: Text("Invited as seller",
            style:  TextStyle(
              fontFamily: "LatoSemibold",
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color:AppColors.writingColor2,
            ),
          ),
        ),
        ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            padding: const EdgeInsets.symmetric(horizontal: 10),
            itemCount:widget.membershipBloc.sentSellerInvites.length,
            itemBuilder:(buildContext,index){
              return Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  verticalSizedBox(15),
                  //Name
                  Text(widget.membershipBloc.sentSellerInvites[index].name!,
                    style:  TextStyle(
                      fontFamily: "RobotoRegular",
                      fontSize: 16,
                      fontWeight: FontWeight.w400,
                      color:AppColors.appBlack,
                    ),
                  ),
                  verticalSizedBox(5),
                  //Number and Status
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      //Number
                      Text("${widget.membershipBloc.sentSellerInvites[index].phoneNumber}",
                        style:  TextStyle(
                          fontFamily: "LatoRegular",
                          fontSize: 12,
                          fontWeight: FontWeight.w400,
                          color:AppColors.appBlack,
                        ),
                      ),
                      horizontalSizedBox(10),
                      //Member
                      Visibility(
                        visible:widget.membershipBloc.sentSellerInvites[index].rank == 2 &&widget.membershipBloc.sentSellerInvites[index].isOnboarded! ,
                        child: Container(
                          alignment: Alignment.center,
                          padding: const EdgeInsets.symmetric(horizontal: 10,vertical: 3),
                          decoration: BoxDecoration(
                              color: AppColors.inActiveGreen,
                              borderRadius: BorderRadius.all(Radius.circular(20))
                          ),
                          child: Text("member",
                            style:  TextStyle(
                              fontFamily: "LatoRegular",
                              fontSize: 12,
                              fontWeight: FontWeight.w400,
                              color:AppColors.appBlack,
                            ),
                          ),
                        ),
                      ),
                      //Seller
                      Visibility(
                        visible:widget.membershipBloc.sentSellerInvites[index].rank == 3 &&widget.membershipBloc.sentSellerInvites[index].isOnboarded! ,
                        child: Container(
                          alignment: Alignment.center,
                          padding: const EdgeInsets.symmetric(horizontal: 10,vertical: 3),
                          decoration: BoxDecoration(
                              color: AppColors.purple,
                              borderRadius: BorderRadius.all(Radius.circular(20))
                          ),
                          child:  Text("seller",
                            style:  TextStyle(
                              fontFamily: "LatoRegular",
                              fontSize: 12,
                              fontWeight: FontWeight.w400,
                              color:AppColors.appBlack,
                            ),
                          ),
                        ),
                      ),

                      Expanded(child: horizontalSizedBox(10)),
                      removeJoinedAndAlreadyJoined(widget.membershipBloc.sentSellerInvites[index])



                      //Joined

                      //Already Joined

                    ],
                  ),


                ],
              );
            }),
      ],
    );
  }
//endregion

  //region Search and filter
  Widget searchFilter() {
    return SizedBox(
      height: 44,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          Expanded(
            child: TextFormField(
              maxLines: 1,
              controller: widget.membershipBloc.sentSearchTextCtrl,
              onChanged: (value){
                widget.membershipBloc.onChangeSentSearchText(value);
              },
              style: TextStyle(
                  fontFamily: "LatoRegular",
                  fontSize: 14,
                  fontWeight: FontWeight.w400,
                  color: AppColors.appBlack),
              decoration: InputDecoration(

                prefixIcon: Padding(
                  padding: const EdgeInsets.all(10),
                  child: SvgPicture.asset(
                    AppImages.searchBarIcon,
                    fit: BoxFit.contain,
                    color: AppColors.appBlack7,
                  ),
                ),
                filled: true,
                contentPadding: const EdgeInsets.symmetric(vertical: 5),
                fillColor: AppColors.textFieldFill1,
                isDense: true,
                hintText: "search people",
                hintStyle: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w400,
                    color: AppColors.writingColor3),
                border: InputBorder.none,
                focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(22),
                    borderSide: BorderSide.none),
                enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(22),
                    borderSide: BorderSide.none),
              ),
            ),
          ),
          // horizontalSizedBox(10),
          // SvgPicture.asset(
          //   AppImages.filter,
          //   color: AppColors.appBlack,
          //   fit: BoxFit.contain,
          // )
        ],
      ),
    );
  }

//endregion



//region Remove, joined and already joined
Widget removeJoinedAndAlreadyJoined(MemberSeller memberSeller){
    ///Remove
    if(memberSeller.isOnboarded==false&&memberSeller.onBoardedByThisUser==false || memberSeller.isOnboarded==true && memberSeller.membershipType != memberSeller.inviteTypeByThisUser ){
      return  InkWell(
        onTap: (){
          widget.membershipBloc.deleteInviteCode(memberSeller.inviteCodeByThisUser!);
        },
        child: Container(
          alignment: Alignment.center,
          padding: const EdgeInsets.symmetric(horizontal: 10,vertical: 5),
          decoration: BoxDecoration(
              color: AppColors.red2,
              borderRadius: BorderRadius.all(Radius.circular(20))
          ),
          child:  Text("remove",
            style:  TextStyle(
              fontFamily: "LatoBold",
              fontSize: 15,
              fontWeight: FontWeight.w700,
              color:AppColors.writingColor2,
            ),
          ),
        ),
      );
    }
    ///Joined
    if(memberSeller.isOnboarded==true&&memberSeller.onBoardedByThisUser==true){
      return   InkWell(
        onTap: (){
          //widget.membershipBloc.onTapInvite();
        },
        child: Container(
          alignment: Alignment.center,
          padding: const EdgeInsets.symmetric(horizontal: 10,vertical: 5),
          decoration: BoxDecoration(
              color: AppColors.red2,
              borderRadius: BorderRadius.all(Radius.circular(20))
          ),
          child:  Text("joined",
            style:  TextStyle(
              fontFamily: "LatoBold",
              fontSize: 15,
              fontWeight: FontWeight.w700,
              color:AppColors.writingColor2,
            ),
          ),
        ),
      );
    }
    ///Already joined
    if(memberSeller.isOnboarded==true&&memberSeller.onBoardedByThisUser==false||memberSeller.isOnboarded==true && memberSeller.membershipType == memberSeller.inviteTypeByThisUser){
      return     Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            alignment: Alignment.center,
            padding: const EdgeInsets.symmetric(horizontal: 10,vertical: 5),
            decoration: BoxDecoration(
                border: Border.all(color: AppColors.lightStroke),
                color: AppColors.appWhite,
                borderRadius: const BorderRadius.all(Radius.circular(20))
            ),
            child:  Text("already joined",
              style:  TextStyle(
                fontFamily: "LatoBold",
                fontSize: 15,
                fontWeight: FontWeight.w700,
                color:AppColors.writingColor2,
              ),
            ),
          ),
          horizontalSizedBox(10),
          InkWell(
              onTap: (){
                widget.membershipBloc.deleteInviteCode(memberSeller.inviteCodeByThisUser!);
              },
              child: SvgPicture.asset(AppImages.close,color: AppColors.appBlack,))
        ],
      );
    }
    return const Text("ttt");
}
//endregion


//endregion



}
